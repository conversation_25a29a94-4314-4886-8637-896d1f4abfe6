package com.flutterup.base.utils

import java.io.File
import java.io.FileInputStream
import java.math.BigInteger
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException

object MD5Utils {

    fun encodeMD5(str: String?): String {
        val buffBytes = encodeMD5Byte(str)
        if (buffBytes == null) return ""

        val encrypt = StringBuilder()
        for (i in buffBytes.indices) {
            if (buffBytes[i].toInt() and 255 < 16) {
                encrypt.append("0")
            }
            encrypt.append((buffBytes[i].toLong() and 255).toString(16))
        }
        return encrypt.toString()
    }

    private fun encodeMD5Byte(str: String?): ByteArray? {
        if (str == null) return null

        val strByte = str.toByteArray()
        return try {
            val e = MessageDigest.getInstance("MD5")
            e.update(strByte)
            e.digest()
        } catch (e: NoSuchAlgorithmException) {
            e.printStackTrace()
            null
        }
    }

    /**
     * 获取单个文件的MD5值
     * @param file 文件
     * @param radix  位 16 32 64
     * @return
     */

    fun getFileMD5(file: File, radix: Int = 16): String? {
        if (!file.isFile) {
            return null
        }
        var digest: MessageDigest? = null
        var inp: FileInputStream? = null
        val buffer = ByteArray(1024)
        var len: Int
        try {
            digest = MessageDigest.getInstance("MD5")
            inp = FileInputStream(file)
            while (inp.read(buffer, 0, 1024).also { len = it } != -1) {
                digest.update(buffer, 0, len)
            }
            inp.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
        val bigInt = BigInteger(1, digest.digest())
        return bigInt.toString(radix)
    }
}