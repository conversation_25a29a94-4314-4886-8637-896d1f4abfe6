package com.flutterup.base.utils

import android.annotation.SuppressLint
import android.provider.Settings
import com.flutterup.base.BaseApplication
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.stringValue
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

object DeviceUtils {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface DeviceUtilsEntryPoint {
        fun getMMKVStore(): MMKVStore
    }

    // 懒加载获取依赖
    private val mmkvStore: MMKVStore by lazy {
        val appContext = BaseApplication.getApplicationContext()
        val entryPoint =
            EntryPointAccessors.fromApplication(appContext, DeviceUtilsEntryPoint::class.java)
        entryPoint.getMMKVStore()
    }

    private var _deviceId by mmkvStore.stringValue("_deviceId")

    fun getDeviceId(): String {
        if (_deviceId.isEmpty()) {
            _deviceId = createDeviceId()
        }
        return _deviceId
    }


    /**
     * 创建设备唯一标识符
     */
    @SuppressLint("HardwareIds")
    private fun createDeviceId(): String {
        val androidId =
            Settings.Secure.getString(BaseApplication.getApplicationContext().contentResolver, Settings.Secure.ANDROID_ID)
        val deviceInfo =
            "${androidId}_${android.os.Build.BRAND}_${android.os.Build.MODEL}_${android.os.Build.PRODUCT}"
        return "device_" + MD5Utils.encodeMD5(deviceInfo)
    }
}
