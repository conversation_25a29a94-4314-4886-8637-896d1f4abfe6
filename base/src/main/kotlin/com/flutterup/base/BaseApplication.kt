package com.flutterup.base

import android.app.Application
import android.content.Context
import com.flutterup.base.utils.Timber
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob


abstract class BaseApplication : Application() {

    companion object {
        private lateinit var context: Application
        private lateinit var scope: CoroutineScope

        fun getApplicationContext(): Context {
            return context
        }

        fun getApplicationScope(): CoroutineScope {
            return scope
        }
    }

    init {
        context = this
        scope = CoroutineScope(SupervisorJob() + Dispatchers.Default + exceptionHandler())
    }

    open fun exceptionHandler(): CoroutineExceptionHandler {
        return CoroutineExceptionHandler { _, exception ->
            Timber.e("Coroutine Exception", exception)
        }
    }
}
