package com.flutterup.base.permission

import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.MMKVStoreImpl
import com.flutterup.base.store.longValue
import com.flutterup.base.store.stringValue
import com.tencent.mmkv.MMKV
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限请求记录存储
 */
@Singleton
class PermissionStorage @Inject constructor() {
    companion object {
        private const val PREFIX_ONCE_PER_APP = "permission_once_app_"
        private const val PREFIX_ONCE_PER_DAY = "permission_once_day_"
        private const val PREFIX_LAST_REQUEST_TIME = "permission_last_request_"
        
        private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    }

    private val mmkvStore = MMKVStoreImpl(mmkv = MMKV.mmkvWithID("permission"))
    
    /**
     * 检查权限是否可以请求（基于限制类型）
     */
    fun canRequestPermission(permission: String, limitType: PermissionLimitType): Boolean {
        return when (limitType) {
            PermissionLimitType.NONE -> true
            PermissionLimitType.ONCE_PER_APP -> !hasRequestedOncePerApp(permission)
            PermissionLimitType.ONCE_PER_DAY -> !hasRequestedToday(permission)
        }
    }
    
    /**
     * 记录权限请求
     */
    fun recordPermissionRequest(permission: String, limitType: PermissionLimitType) {
        val currentTime = System.currentTimeMillis()
        
        when (limitType) {
            PermissionLimitType.ONCE_PER_APP -> {
                mmkvStore.put("$PREFIX_ONCE_PER_APP$permission", true)
            }
            PermissionLimitType.ONCE_PER_DAY -> {
                val today = dateFormat.format(Date(currentTime))
                mmkvStore.put("$PREFIX_ONCE_PER_DAY$permission", today)
            }
            PermissionLimitType.NONE -> {
                // 不需要记录
            }
        }
        
        // 记录最后请求时间
        mmkvStore.put("$PREFIX_LAST_REQUEST_TIME$permission", currentTime)
    }
    
    /**
     * 检查权限是否已经在应用内请求过一次
     */
    private fun hasRequestedOncePerApp(permission: String): Boolean {
        return mmkvStore.get("$PREFIX_ONCE_PER_APP$permission", false)
    }
    
    /**
     * 检查权限今天是否已经请求过
     */
    private fun hasRequestedToday(permission: String): Boolean {
        val lastRequestDay = mmkvStore.get("$PREFIX_ONCE_PER_DAY$permission", "")
        val today = dateFormat.format(Date())
        return lastRequestDay == today
    }
    
    /**
     * 获取权限最后请求时间
     */
    fun getLastRequestTime(permission: String): Long {
        return mmkvStore.get("$PREFIX_LAST_REQUEST_TIME$permission", 0L)
    }
    
    /**
     * 清除权限请求记录
     */
    fun clearPermissionRecord(permission: String) {
        mmkvStore.remove("$PREFIX_ONCE_PER_APP$permission")
        mmkvStore.remove("$PREFIX_ONCE_PER_DAY$permission")
        mmkvStore.remove("$PREFIX_LAST_REQUEST_TIME$permission")
    }
    
    /**
     * 清除所有权限请求记录
     */
    fun clearAllRecords() {
        mmkvStore.clear()
    }
}
