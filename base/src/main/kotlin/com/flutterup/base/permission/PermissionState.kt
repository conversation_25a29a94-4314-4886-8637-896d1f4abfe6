package com.flutterup.base.permission

import android.content.pm.PackageManager

/**
 * 权限状态枚举
 */
enum class PermissionStatus {
    /** 已授权 */
    GRANTED,
    /** 被拒绝 */
    DENIED,
    /** 被永久拒绝（不再询问） */
    PERMANENTLY_DENIED,
    /** 未知状态 */
    UNKNOWN
}

/**
 * 权限请求结果
 */
data class PermissionResult(
    val permission: String,
    val status: PermissionStatus,
    val isGranted: Boolean = status == PermissionStatus.GRANTED
) {
    companion object {
        fun fromGrantResult(permission: String, grantResult: Int): PermissionResult {
            val status = when (grantResult) {
                PackageManager.PERMISSION_GRANTED -> PermissionStatus.GRANTED
                PackageManager.PERMISSION_DENIED -> PermissionStatus.DENIED
                else -> PermissionStatus.UNKNOWN
            }
            return PermissionResult(permission, status)
        }
    }
}

/**
 * 多权限请求结果
 */
data class MultiplePermissionResult(
    val results: Map<String, PermissionResult>
) {
    val allGranted: <PERSON><PERSON><PERSON>
        get() = results.values.all { it.isGranted }
    
    val anyGranted: <PERSON><PERSON><PERSON>
        get() = results.values.any { it.isGranted }
    
    val grantedPermissions: List<String>
        get() = results.filter { it.value.isGranted }.keys.toList()
    
    val deniedPermissions: List<String>
        get() = results.filter { !it.value.isGranted }.keys.toList()
    
    val permanentlyDeniedPermissions: List<String>
        get() = results.filter { it.value.status == PermissionStatus.PERMANENTLY_DENIED }.keys.toList()
}

/**
 * 权限请求限制类型
 */
enum class PermissionLimitType {
    /** 无限制 */
    NONE,
    /** 应用内只请求一次 */
    ONCE_PER_APP,
    /** 每天只请求一次 */
    ONCE_PER_DAY
}

/**
 * 权限请求前的自定义操作
 */
fun interface PermissionPreAction {
    /**
     * 执行自定义操作
     * @param permissions 要请求的权限列表
     * @param onComplete 完成回调，传入true继续请求权限，false取消请求
     */
    suspend fun execute(permissions: List<String>, onComplete: (Boolean) -> Unit)
}

/**
 * 权限请求配置
 */
data class PermissionRequestConfig(
    /** 权限列表 */
    val permissions: List<String>,
    /** 限制类型 */
    val limitType: PermissionLimitType = PermissionLimitType.NONE,
    /** 权限请求前的自定义操作 */
    val preAction: PermissionPreAction? = null,
    /** 权限是否需要说明 */
    val showRationale: Boolean = true,
)

/**
 * 权限请求回调
 */
interface PermissionCallback {
    fun onGranted(result: MultiplePermissionResult)
    fun onDenied(result: MultiplePermissionResult)
    fun onShowRationale(permissions: List<String>, proceed: () -> Unit, cancel: () -> Unit)
}
