# MMKV Store 封装

这是一个基于 MMKV 的类型安全存储封装，支持 Kotlin 委托属性和依赖注入。

## 特性

- 🎯 类型安全的存储访问
- 🔧 Kotlin 委托属性支持
- 💉 Hilt 依赖注入集成
- 📦 支持复杂对象JSON序列化
- 👀 观察者模式支持
- 🚀 高性能的 MMKV 底层实现

## 基本用法

### 1. 依赖注入使用

```kotlin
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    @Inject
    lateinit var store: MMKVStore
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 直接使用
        store.put("key", "value")
        val value = store.get("key", "default")
    }
}
```

### 2. 委托属性使用

```kotlin
class UserPreferences @Inject constructor(
    private val store: MMKVStore
) {
    // 基本类型
    var username by store.stringValue()
    var userId by store.intValue()
    var isLoggedIn by store.booleanValue()
    
    // 自定义key
    var token by store.stringValue(key = "access_token")
    
    // 可空值
    var nickname by store.nullableStringValue()
    
    // 带默认值
    var theme by store.stringValue(defaultValue = "dark")
}
```

### 3. 复杂对象存储

```kotlin
data class User(val id: String, val name: String, val email: String)

class UserRepository @Inject constructor(
    private val store: MMKVStore,
    private val moshi: Moshi
) {
    var currentUser by store.jsonValue<User>(moshi = moshi)
    var userList by store.listValue<User>(moshi = moshi)
}
```

### 4. 观察者模式

```kotlin
class ObservablePreferences @Inject constructor(
    store: MMKVStore
) {
    private val observableStore = ObservableMMKVStore(store)
    
    var setting by observableStore.stringValue()
    
    init {
        observableStore.addListener(object : MMKVChangeListener {
            override fun onChanged(key: String) {
                println("Key $key changed")
            }
        })
    }
}
```

### 5. 批量操作

```kotlin
store.batch {
    put("key1", "value1")
    put("key2", 42)
    put("key3", true)
}
```

## 注意事项

- MMKV 会在 `StoreModule` 中自动初始化
- 支持的基本类型：String, Int, Long, Float, Double, Boolean, ByteArray
- 复杂对象需要通过 JSON 序列化存储
- 所有操作都是线程安全的