package com.flutterup.base.store

import com.flutterup.base.utils.JsonUtils
import com.squareup.moshi.JsonAdapter
import com.squareup.moshi.Moshi
import com.squareup.moshi.Types
import kotlin.reflect.KClass

// JSON序列化扩展，支持复杂对象存储
inline fun <reified T> MMKVStore.jsonValue(
    key: String? = null,
    defaultValue: T? = null,
    jsonUtils: JsonUtils
): JsonMMKVDelegate<T> {
    return JsonMMKVDelegate(this, key, defaultValue, jsonUtils, T::class)
}

class JsonMMKVDelegate<T>(
    private val store: MMKVStore,
    private val key: String? = null,
    private val defaultValue: T? = null,
    private val jsonUtils: JsonUtils,
    private val clazz: KClass<*>
) : kotlin.properties.ReadWriteProperty<Any?, T?> {

    private val adapter: JsonAdapter<T> by lazy {
        @Suppress("UNCHECKED_CAST")
        jsonUtils.getAdapter(clazz.java) as JsonAdapter<T>
    }

    override fun getValue(thisRef: Any?, property: kotlin.reflect.KProperty<*>): T? {
        val actualKey = key ?: property.name
        val json = store.get(actualKey, "")
        return if (json.isEmpty()) {
            defaultValue
        } else {
            try {
                adapter.fromJson(json)
            } catch (_: Exception) {
                defaultValue
            }
        }
    }

    override fun setValue(thisRef: Any?, property: kotlin.reflect.KProperty<*>, value: T?) {
        val actualKey = key ?: property.name
        if (value == null) {
            store.remove(actualKey)
        } else {
            val json = adapter.toJson(value)
            store.put(actualKey, json)
        }
    }
}

// 列表类型支持
inline fun <reified T> MMKVStore.listValue(
    key: String? = null,
    defaultValue: List<T> = emptyList(),
    jsonUtils: JsonUtils
): ListMMKVDelegate<T> {
    return ListMMKVDelegate(this, key, defaultValue, jsonUtils)
}

class ListMMKVDelegate<T>(
    private val store: MMKVStore,
    private val key: String? = null,
    private val defaultValue: List<T> = emptyList(),
    private val jsonUtils: JsonUtils
) : kotlin.properties.ReadWriteProperty<Any?, List<T>> {

    private val adapter: JsonAdapter<List<T>> by lazy {
        jsonUtils.getAdapter<List<T>>()
    }

    override fun getValue(thisRef: Any?, property: kotlin.reflect.KProperty<*>): List<T> {
        val actualKey = key ?: property.name
        val json = store.get(actualKey, "")
        return if (json.isEmpty()) {
            defaultValue
        } else {
            try {
                adapter.fromJson(json) ?: defaultValue
            } catch (_: Exception) {
                defaultValue
            }
        }
    }

    override fun setValue(thisRef: Any?, property: kotlin.reflect.KProperty<*>, value: List<T>) {
        val actualKey = key ?: property.name
        val json = adapter.toJson(value)
        store.put(actualKey, json)
    }
}

// 批量操作扩展
fun MMKVStore.batch(action: MMKVStore.() -> Unit) {
    action()
}

// 观察者模式扩展（可选）
interface MMKVChangeListener {
    fun onChanged(key: String)
}

// 可监听的存储包装器
class ObservableMMKVStore(
    private val delegate: MMKVStore
) : MMKVStore by delegate {

    private val listeners = mutableSetOf<MMKVChangeListener>()

    fun addListener(listener: MMKVChangeListener) {
        listeners.add(listener)
    }

    fun removeListener(listener: MMKVChangeListener) {
        listeners.remove(listener)
    }

    override fun put(key: String, value: Any?) {
        delegate.put(key, value)
        notifyListeners(key)
    }

    override fun remove(key: String) {
        delegate.remove(key)
        notifyListeners(key)
    }

    override fun clear() {
        delegate.clear()
        listeners.forEach { it.onChanged("*") }
    }

    private fun notifyListeners(key: String) {
        listeners.forEach { it.onChanged(key) }
    }
}