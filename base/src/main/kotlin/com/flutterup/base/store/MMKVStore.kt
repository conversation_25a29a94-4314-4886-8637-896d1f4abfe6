package com.flutterup.base.store

import android.os.Parcelable
import com.tencent.mmkv.MMKV
import kotlinx.serialization.Serializable
import kotlin.reflect.KProperty

interface MMKVStore {
    fun put(key: String, value: Any?)
    fun <T> get(key: String, defaultValue: T): T
    fun remove(key: String)
    fun clear()
    fun contains(key: String): Boolean
}

class MMKVStoreImpl(
    private val mmkv: MMKV = MMKV.defaultMMKV()
) : MMKVStore {

    override fun put(key: String, value: Any?) {
        when (value) {
            null -> mmkv.removeValueForKey(key)
            is String -> mmkv.encode(key, value)
            is Int -> mmkv.encode(key, value)
            is Long -> mmkv.encode(key, value)
            is Float -> mmkv.encode(key, value)
            is Double -> mmkv.encode(key, value)
            is Boolean -> mmkv.encode(key, value)
            is ByteArray -> mmkv.encode(key, value)
            is Parcelable -> mmkv.encode(key, value)
            else -> throw IllegalArgumentException("Unsupported type: ${value::class.java}")
        }
    }

    @Suppress("UNCHECKED_CAST")
    override fun <T> get(key: String, defaultValue: T): T {
        return when (defaultValue) {
            is String -> mmkv.decodeString(key, defaultValue) as T
            is Int -> mmkv.decodeInt(key, defaultValue) as T
            is Long -> mmkv.decodeLong(key, defaultValue) as T
            is Float -> mmkv.decodeFloat(key, defaultValue) as T
            is Double -> mmkv.decodeDouble(key, defaultValue) as T
            is Boolean -> mmkv.decodeBool(key, defaultValue) as T
            is ByteArray -> mmkv.decodeBytes(key, defaultValue) as T
            is Parcelable -> mmkv.decodeParcelable(key, defaultValue.javaClass, defaultValue) as T
            else -> throw IllegalArgumentException("Unsupported type: ${defaultValue?.let { it::class.java }}")
        }
    }

    override fun remove(key: String) {
        mmkv.removeValueForKey(key)
    }

    override fun clear() {
        mmkv.clearAll()
    }

    override fun contains(key: String): Boolean {
        return mmkv.containsKey(key)
    }
}