package com.flutterup.tracking

import android.content.Context
import androidx.startup.Initializer
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustConfig
import com.adjust.sdk.LogLevel
import com.flutterup.base.BuildConfig
import com.flutterup.base.utils.Timber

/**
 * Adjust SDK初始化器
 */
class AdjustInitializer : Initializer<Unit> {

    private val tag = "AdjustInitializer"

    override fun create(context: Context) {
        try {
            val environment = if (BuildConfig.DEBUG) {
                AdjustConfig.ENVIRONMENT_SANDBOX
            } else {
                AdjustConfig.ENVIRONMENT_PRODUCTION
            }

            val adjustAppToken = "jdro34o6wfeo"
            val config = AdjustConfig(context, adjustAppToken, environment)

            // 启用日志（仅在Debug模式下）
            if (BuildConfig.DEBUG) {
                config.setLogLevel(LogLevel.VERBOSE)
            }

            // 初始化Adjust
            Adjust.initSdk(config)

            Timber.i(tag, "Adjust SDK initialized successfully")
        } catch (e: Exception) {
            Timber.e(tag, "Failed to initialize Adjust SDK", e)
        }
    }

    override fun dependencies(): List<Class<out Initializer<*>?>> = emptyList()
}
