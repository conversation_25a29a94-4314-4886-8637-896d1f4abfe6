package com.flutterup.tracking

/**
 * 埋点事件数据模型
 */
data class TrackingEvent(
    /**
     * 事件类型
     */
    val eventType: TrackingEventType,
    
    /**
     * 事件名称，如果为空则使用eventType的默认名称
     */
    val eventName: String? = null,
    
    /**
     * 事件属性
     */
    val properties: Map<String, Any> = emptyMap(),
    
    /**
     * 上报配置，如果为空则使用默认配置
     */
    val config: TrackingConfig? = null
) {
    /**
     * 获取最终的事件名称
     */
    fun getFinalEventName(): String {
        return eventName ?: eventType.eventName
    }
}
