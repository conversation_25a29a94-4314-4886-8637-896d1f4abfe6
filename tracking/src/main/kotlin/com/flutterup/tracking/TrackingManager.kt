package com.flutterup.tracking

import com.flutterup.base.BaseApplication
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.nullableStringValue
import com.flutterup.base.utils.Timber
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.launch
import kotlin.getValue

/**
 * 埋点管理器 - 主要入口类
 */

object TrackingManager {

    private const val TAG = "TrackingManager"

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface TrackingManagerEntryPoint {
        fun provideTrackingServiceFactory(): TrackingServiceFactory

        fun provideMMKVStore(): MMKVStore
    }

    private val trackingServiceFactory: TrackingServiceFactory by lazy {
        val appContext = BaseApplication.getApplicationContext()
        val entryPoint =
            EntryPointAccessors.fromApplication(appContext, TrackingManagerEntryPoint::class.java)
        entryPoint.provideTrackingServiceFactory()
    }

    private val mmkvStore: MMKVStore by lazy {
        val appContext = BaseApplication.getApplicationContext()
        val entryPoint =
            EntryPointAccessors.fromApplication(appContext, TrackingManagerEntryPoint::class.java)
        entryPoint.provideMMKVStore()
    }

    private var _trackingId: String? by mmkvStore.nullableStringValue("tracking_id")


    private val scope = BaseApplication.getApplicationScope()

    // 默认配置
    private var defaultConfig = TrackingConfig()

    // 全局动态预置属性提供者
    private var globalDynamicPresetPropertiesProvider: (() -> Map<String, Any>)? = null

    init {
        trackingServiceFactory.getDeviceId {
            if (it.isEmpty()) return@getDeviceId
            _trackingId = it
        }
    }

    /**
     * 设置默认配置
     */
    fun setDefaultConfig(config: TrackingConfig) {
        defaultConfig = config
        Timber.d(TAG, "Default config updated: $config")
    }

    /**
     * 获取默认配置
     */
    fun getDefaultConfig(): TrackingConfig = defaultConfig

    /**
     * 页面曝光埋点
     */
    fun trackViewScreen(
        properties: Map<String, Any> = emptyMap(),
        config: TrackingConfig? = null
    ) {
        val event = TrackingEvent(
            eventType = TrackingEventType.VIEW_SCREEN,
            properties = properties,
            config = config
        )
        trackEvent(event)
    }

    /**
     * 弹窗曝光埋点
     */
    fun trackDialog(
        properties: Map<String, Any> = emptyMap(),
        config: TrackingConfig? = null
    ) {
        val event = TrackingEvent(
            eventType = TrackingEventType.DIALOG,
            properties = properties,
            config = config
        )
        trackEvent(event)
    }

    /**
     * 点击事件埋点
     */
    fun trackClick(
        properties: Map<String, Any> = emptyMap(),
        config: TrackingConfig? = null
    ) {
        val event = TrackingEvent(
            eventType = TrackingEventType.CLICK,
            properties = properties,
            config = config
        )
        trackEvent(event)
    }

    /**
     * 自定义事件埋点
     */
    fun trackCustom(
        eventName: String,
        properties: Map<String, Any> = emptyMap(),
        config: TrackingConfig? = null
    ) {
        val event = TrackingEvent(
            eventType = TrackingEventType.CUSTOM,
            eventName = eventName,
            properties = properties,
            config = config
        )
        trackEvent(event)
    }

    /**
     * 通用埋点方法
     */
    fun trackEvent(event: TrackingEvent) {
        scope.launch {
            try {
                val finalConfig = event.config ?: defaultConfig
                val trackingService = trackingServiceFactory.createTrackingService(finalConfig.reportType)

                // 收集所有预置属性
                val allPresetProperties = mutableMapOf<String, Any>()

                // 1. 添加静态预置属性
                allPresetProperties.putAll(finalConfig.presetProperties)

                // 2. 添加全局动态预置属性
                globalDynamicPresetPropertiesProvider?.let { provider ->
                    try {
                        val dynamicProperties = provider()
                        allPresetProperties.putAll(dynamicProperties)
                        Timber.d(TAG, "Added global dynamic preset properties: $dynamicProperties")
                    } catch (e: Exception) {
                        Timber.e(TAG, "Failed to get global dynamic preset properties", e)
                    }
                }

                // 3. 添加配置中的动态预置属性
                finalConfig.dynamicPresetPropertiesProvider?.let { provider ->
                    try {
                        val dynamicProperties = provider()
                        allPresetProperties.putAll(dynamicProperties)
                        Timber.d(TAG, "Added config dynamic preset properties: $dynamicProperties")
                    } catch (e: Exception) {
                        Timber.e(TAG, "Failed to get config dynamic preset properties", e)
                    }
                }

                // 设置合并后的预置属性
                if (allPresetProperties.isNotEmpty()) {
                    trackingService.setPresetProperties(allPresetProperties)
                }

                trackingService.track(event)

            } catch (e: Exception) {
                Timber.e(TAG, "Failed to track event: ${event.getFinalEventName()}", e)
            }
        }
    }

    /**
     * 设置全局预置属性
     */
    fun setGlobalPresetProperties(properties: Map<String, Any>) {
        defaultConfig = defaultConfig.copy(presetProperties = properties)
        Timber.d(TAG, "Global preset properties updated: $properties")
    }

    /**
     * 设置全局动态预置属性提供者
     * 在每次发送埋点时会调用此函数获取最新的预置属性
     */
    fun setGlobalDynamicPresetPropertiesProvider(provider: (() -> Map<String, Any>)?) {
        globalDynamicPresetPropertiesProvider = provider
        Timber.d(TAG, "Global dynamic preset properties provider updated")
    }

    /**
     * 获取埋点设备ID
     *
     * @return 埋点设备ID
     */
    fun getTrackingId(): String? {
        return _trackingId
    }
}
