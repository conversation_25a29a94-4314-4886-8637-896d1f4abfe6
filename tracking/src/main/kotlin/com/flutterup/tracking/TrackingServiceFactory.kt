package com.flutterup.tracking

import javax.inject.Inject
import javax.inject.Singleton

/**
 * 埋点服务工厂
 */
@Singleton
class TrackingServiceFactory @Inject constructor(
    private val adjustTrackingService: AdjustTrackingService,
    private val internalTrackingService: InternalTrackingService
) {

    /**
     * 根据配置创建对应的埋点服务
     */
    fun createTrackingService(reportType: TrackingReportType): TrackingService {
        return when (reportType) {
            TrackingReportType.ADJUST -> adjustTrackingService
            TrackingReportType.INTERNAL -> internalTrackingService
        }
    }

    fun getDeviceId(callback: (String) -> Unit) {
        adjustTrackingService.getTrackingId(callback)
    }
}
