package com.flutterup.tracking

import com.flutterup.network.BaseResponse
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.POST

interface TrackingApiService {

    @FormUrlEncoded
    @POST("/app/event/trace")
    suspend fun tracking(
        @Field("key") key: String,
        @Field("param") value: String
    ): BaseResponse<Any?>


    @FormUrlEncoded
    @POST("/pay/trace")
    suspend fun trackingPay(
        @Field("event_id") eventId: Int,
        @Field("event_from") eventFrom: Int,
        @Field("event_param") eventParam: Int,
        @Field("event_mid") eventModelId: String? = null,
        @Field("extra") eventExtra: String? = null,
    ) : BaseResponse<Any?>
}