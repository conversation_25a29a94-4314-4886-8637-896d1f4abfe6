package com.flutterup.tracking.di

import com.flutterup.network.NetworkService
import com.flutterup.tracking.*
import com.squareup.moshi.Moshi
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object TrackModule {

    @Singleton
    @Provides
    fun provideTrackingApiService(networkService: NetworkService): TrackingApiService {
        return networkService[TrackingApiService::class.java]
    }

    @Singleton
    @Provides
    fun provideAdjustTrackingService(): AdjustTrackingService {
        return AdjustTrackingService()
    }

    @Singleton
    @Provides
    fun provideInternalTrackingService(
        trackingApiService: TrackingApiService,
        moshi: Moshi
    ): InternalTrackingService {
        return InternalTrackingService(trackingApiService, moshi)
    }

    @Singleton
    @Provides
    fun provideTrackingServiceFactory(
        adjustTrackingService: AdjustTrackingService,
        internalTrackingService: InternalTrackingService
    ): TrackingServiceFactory {
        return TrackingServiceFactory(adjustTrackingService, internalTrackingService)
    }
}