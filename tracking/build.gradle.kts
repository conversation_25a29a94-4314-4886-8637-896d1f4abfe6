plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.kotlin.android)
}

apply(from = "${rootDir}/gradles/buildBase.gradle")
apply(from = "${rootDir}/gradles/buildHilt.gradle")

android {
    namespace = "com.flutterup.tracking"
}

dependencies {
    implementation(project(":base"))
    implementation(project(":network"))

    implementation(libs.adjust)
    implementation(libs.adjust.installreferrer)
    implementation(libs.google.services.ads.identifier)
}