# 埋点系统 (Tracking System)

这是一个基于工厂模式和Hilt依赖注入的埋点系统，支持Adjust SDK和内部API两种上报方式。

## 特性

- 🏭 **工厂模式** - 支持多种埋点上报方式
- 💉 **Hilt依赖注入** - 完全集成到项目的DI系统中
- 📊 **多种事件类型** - ViewScreen、Dialog、Click、Custom
- 🔄 **灵活配置** - 支持Adjust和内部API两种上报方式
- 📝 **Timber日志** - 使用base模块的Timber进行日志记录
- ⚙️ **预置属性** - 支持设置全局和事件级别的预置属性

## 支持的事件类型

| 事件类型       | 默认事件名      | 说明             |
|------------|------------|----------------|
| ViewScreen | ViewScreen | 页面曝光           |
| Dialog     | Dialog     | 弹窗曝光           |
| Click      | Click      | 点击事件           |
| Custom     | Custom     | 自定义事件（可自定义事件名） |

## 上报方式

- **Adjust** - 使用Adjust SDK进行埋点上报
- **Internal** - 使用项目内部API进行埋点上报

## 快速开始

### 1. 依赖注入

在需要使用埋点的类中注入`TrackingManager`：

```kotlin
@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var trackingManager: TrackingManager

    // ...
}
```

### 2. 基本使用

```kotlin
// 页面曝光
trackingManager.trackViewScreen(
    screenName = "HomePage",
    properties = mapOf("user_id" to "12345")
)

// 弹窗曝光
trackingManager.trackDialog(
    dialogName = "LoginDialog",
    properties = mapOf("trigger" to "auto_popup")
)

// 点击事件
trackingManager.trackClick(
    elementName = "LoginButton",
    properties = mapOf("button_text" to "登录")
)

// 自定义事件
trackingManager.trackCustom(
    eventName = "UserPurchase",
    properties = mapOf(
        "product_id" to "product_123",
        "price" to 99.99
    )
)
```

### 3. 配置上报方式

```kotlin
// 设置默认配置
val config = TrackingConfig(
    reportType = TrackingReportType.ADJUST, // 或 INTERNAL
    enableLogging = true,
    presetProperties = mapOf(
        "app_version" to "1.0.0",
        "platform" to "android"
    )
)
trackingManager.setDefaultConfig(config)

// 为特定事件指定配置
trackingManager.trackCustom(
    eventName = "SpecialEvent",
    properties = mapOf("key" to "value"),
    config = TrackingConfig(reportType = TrackingReportType.INTERNAL)
)
```

### 4. 预置属性

```kotlin
// 设置全局预置属性（静态）
trackingManager.setGlobalPresetProperties(
    mapOf(
        "device_id" to "device_123",
        "user_type" to "premium"
    )
)

// 设置全局动态预置属性提供者
trackingManager.setGlobalDynamicPresetPropertiesProvider {
    mapOf(
        "current_timestamp" to System.currentTimeMillis(),
        "session_id" to getCurrentSessionId(),
        "user_id" to getCurrentUserId(),
        "network_type" to getCurrentNetworkType()
    )
}
```

### 5. 动态预置属性

动态预置属性允许你在每次发送埋点时获取最新的属性值，适用于那些经常变化的属性：

```kotlin
// 方式1：全局动态预置属性（对所有埋点生效）
trackingManager.setGlobalDynamicPresetPropertiesProvider {
    mapOf(
        "timestamp" to System.currentTimeMillis(),
        "memory_usage" to getCurrentMemoryUsage(),
        "battery_level" to getBatteryLevel()
    )
}

// 方式2：配置级动态预置属性（仅对特定配置生效）
val configWithDynamicProperties = TrackingConfig(
    reportType = TrackingReportType.ADJUST,
    dynamicPresetPropertiesProvider = {
        mapOf(
            "page_load_time" to getPageLoadTime(),
            "network_latency" to getNetworkLatency()
        )
    }
)

trackingManager.trackCustom(
    eventName = "PagePerformance",
    config = configWithDynamicProperties
)
```

## 配置说明

### TrackingConfig

| 参数               | 类型                 | 默认值        | 说明     |
|------------------|--------------------|------------|--------|
| reportType       | TrackingReportType | INTERNAL   | 上报方式   |
| enableLogging    | Boolean            | true       | 是否启用日志 |
| presetProperties | Map<String, Any>   | emptyMap() | 静态预置属性   |
| dynamicPresetPropertiesProvider | (() -> Map<String, Any>)? | null | 动态预置属性提供者 |

### TrackingReportType

- `ADJUST` - 使用Adjust SDK上报
- `INTERNAL` - 使用内部API上报

## Adjust配置

系统会自动初始化Adjust SDK，使用`KeyValues.ADJUST_APP_TOKEN`作为App Token。

需要在`AdjustTrackingService.getAdjustToken()`方法中配置具体的事件Token：

```kotlin
private fun getAdjustToken(eventName: String): String {
    return when (eventName) {
        "ViewScreen" -> "your_view_screen_token"
        "Dialog" -> "your_dialog_token"
        "Click" -> "your_click_token"
        else -> "your_default_token"
    }
}
```

## 注意事项

1. **Adjust Token配置** - 需要根据实际的Adjust配置更新事件Token
2. **线程安全** - 所有埋点操作都在IO线程中执行
3. **错误处理** - 埋点失败会记录错误日志，不会影响主流程
4. **属性合并** - 预置属性和事件属性会自动合并，事件属性优先级更高
5. **动态预置属性** - 动态预置属性提供者会在每次埋点时调用，请确保：
   - 提供者函数执行速度快，避免阻塞埋点发送
   - 提供者函数不会抛出异常，如有异常会被捕获并记录日志
   - 属性合并优先级：事件属性 > 配置动态预置属性 > 全局动态预置属性 > 静态预置属性

## 示例代码

详细的使用示例请参考 `TrackingUsageExample.kt` 文件。
