android {
    compileSdk 36

    defaultConfig {
        minSdk 24

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'

        ndk {
            //noinspection ChromeOsAbiSupport
            abiFilters "armeabi-v7a", "arm64-v8a"
        }

        packagingOptions {
            exclude "lib/x86/lib*.so"
            exclude "lib/x86_64/lib*.so"
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            consumerProguardFiles file('.').listFiles(new FilenameFilter() {
                @Override
                boolean accept(File file, String s) {
                    return s.endsWith('.pro')
                }
            })
        }
        release {
            minifyEnabled true
            consumerProguardFiles file('.').listFiles(new FilenameFilter() {
                @Override
                boolean accept(File file, String s) {
                    return s.endsWith('.pro')
                }
            })
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }

    buildFeatures {
        buildConfig = true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)


    testImplementation(libs.mockk)
    testImplementation(libs.mockk.agent)
    testImplementation(libs.coroutines.test)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}