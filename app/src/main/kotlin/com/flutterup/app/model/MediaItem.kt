package com.flutterup.app.model

import android.os.Parcelable
import com.squareup.moshi.Json
import kotlinx.parcelize.Parcelize

@Parcelize
data class MediaItem(
    @Json(name = "type") val type: Int,

    @<PERSON><PERSON>(name = "url") val url: String,

    @<PERSON><PERSON>(name = "width") val width: Long? = null,

    @<PERSON><PERSON>(name = "height") val height: Long? = null,

    @<PERSON><PERSON>(name = "size") val size: Long? = null,

    @<PERSON><PERSON>(name = "duration")
    val duration: Long? = null,

    @<PERSON><PERSON>(name = "is_private") val isPrivate: Int? = null,

    @<PERSON><PERSON>(name = "thumb_url") val thumbUrl: String? = null,

    @<PERSON><PERSON>(name = "version") val version: Int? = null,

    @<PERSON><PERSON>(name = "mirror_url") val mirrorUrl: String? = null,

    @<PERSON><PERSON>(name = "thumb_mirror_url") val thumbMirrorUrl: String? = null
) : Parcelable {

    fun isImage(): Boolean = type == TYPE_IMAGE

    fun isVideo(): Boolean = type == TYPE_VIDEO

    val typedUrl: String
        get() = if (isImage()) (mirrorUrl ?: url) else (thumbMirrorUrl ?: thumbUrl ?: url)

    fun isPrivate(): Boolean {
        return isPrivate == 1
    }

    companion object {
        const val TYPE_IMAGE = 0
        const val TYPE_VIDEO = 1
    }
}