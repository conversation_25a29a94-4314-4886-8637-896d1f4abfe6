package com.flutterup.app.screen.login

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import kotlinx.serialization.Serializable

@Serializable
data object LoginRoute

@Serializable
data object LoginBaseRoute


fun NavGraphBuilder.loginGraph() {
    navigation<LoginBaseRoute>(startDestination = LoginRoute::class) {
        composable(route = LoginRoute::class) {
            LoginScreen()
        }
    }
}