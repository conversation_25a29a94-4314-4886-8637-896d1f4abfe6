package com.flutterup.app.screen.login

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.R
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText


@Composable
fun GoogleSignInOutlinedButton(
    clientId: String,
    modifier: Modifier = Modifier,
) {
    OutlinedButton(
        onClick = {

        },
        modifier = modifier
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_google),
                contentDescription = null,
                modifier = Modifier.size(18.dp),
                tint = Color.Unspecified
            )

            Spacer(modifier = Modifier.width(8.dp))

            BodyText(
                text = stringResource(R.string.sign_in_with_google),
                size = BodySize.Large
            )
        }
    }
}

@Preview(backgroundColor = 0x000000)
@Composable
private fun GoogleSignInOutlinedButtonPreview() {
    GoogleSignInOutlinedButton(
        clientId = "your_client_id",
        modifier = Modifier.fillMaxWidth().height(50.dp),
    )
}