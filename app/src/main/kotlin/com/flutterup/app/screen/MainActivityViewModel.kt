package com.flutterup.app.screen

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.flutterup.app.model.UserInfo
import com.flutterup.app.repository.UserRepository
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class MainActivityViewModel @Inject constructor(
    private val userRepository: UserRepository,
) : ViewModel() {

    companion object {
        private const val TAG = "MainActivityViewModel"
        private const val MIN_SPLASH_DURATION = 1000L // 最小启动页显示时间
    }

    private val _uiState = MutableStateFlow<MainActivityUIState>(MainActivityUIState.None)
    val uiState: StateFlow<MainActivityUIState> = _uiState.asStateFlow()

    init {
        checkAuthenticationState()
    }

    /**
     * 检查用户认证状态
     */
    private fun checkAuthenticationState() {
        viewModelScope.launch {
            try {
                Timber.d(TAG, "Checking authentication state...")

                // 确保启动页至少显示指定时间
                val startTime = System.currentTimeMillis()

                // 检查用户登录状态
                val isLoggedIn = userRepository.isLogin
                val userInfo = userRepository.userInfo

                Timber.d(TAG, "User login status: $isLoggedIn, userInfo: ${userInfo != null}")

                // 计算剩余等待时间
                val elapsedTime = System.currentTimeMillis() - startTime
                val remainingTime = MIN_SPLASH_DURATION - elapsedTime
                if (remainingTime > 0) {
                    delay(remainingTime)
                }

                // 更新UI状态
                _uiState.value = when {
                    isLoggedIn && userInfo != null -> {
                        Timber.d(TAG, "User authenticated successfully")
                        MainActivityUIState.Success(userInfo)
                    }
                    else -> {
                        Timber.d(TAG, "User not authenticated")
                        MainActivityUIState.Unauthorized
                    }
                }

            } catch (e: Exception) {
                Timber.e(TAG, "Error checking authentication state", e)
                _uiState.value = MainActivityUIState.Unauthorized
            }
        }
    }

    /**
     * 手动刷新认证状态
     */
    fun refreshAuthenticationState() {
        _uiState.value = MainActivityUIState.None
        checkAuthenticationState()
    }

    /**
     * 用户登录成功后调用
     */
    fun onUserLoggedIn(userInfo: UserInfo) {
        Timber.d(TAG, "User logged in: ${userInfo.userId}")
        _uiState.value = MainActivityUIState.Success(userInfo)
    }

    /**
     * 用户登出
     */
    fun onUserLoggedOut() {
        Timber.d(TAG, "User logged out")
        userRepository.logout()
        _uiState.value = MainActivityUIState.Unauthorized
    }
}

sealed interface MainActivityUIState {
    data object None : MainActivityUIState

    data object Unauthorized : MainActivityUIState

    data class Success(val userInfo: UserInfo) : MainActivityUIState


    /**
     * Returns `true` if the state wasn't loaded yet and it should keep showing the splash screen.
     */
    fun shouldKeepSplashScreen() = this is None
}