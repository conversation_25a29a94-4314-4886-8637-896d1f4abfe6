package com.flutterup.app.screen.login

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.flutterup.app.auth.GoogleAuthRepository
import com.flutterup.app.auth.GoogleAuthState
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(
    private val googleAuthRepository: GoogleAuthRepository
) : ViewModel() {

    companion object {
        private const val TAG = "LoginViewModel"
    }

    /**
     * Google认证状态
     */
    val googleAuthState: StateFlow<GoogleAuthState> = googleAuthRepository.authState

    /**
     * 调用Google Web API去登录
     */
    fun loginWithGoogle(clientId: String) {
        viewModelScope.launch {
            try {
                Timber.d(TAG, "Initiating Google login")
                googleAuthRepository.signInWithGoogle(clientId)
            } catch (e: Exception) {
                Timber.e(TAG, "Error during Google login", e)
            }
        }
    }

    /**
     * 重置Google认证状态
     */
    fun resetGoogleAuthState() {
        googleAuthRepository.resetAuthState()
    }
}
