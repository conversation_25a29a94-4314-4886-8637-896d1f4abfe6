package com.flutterup.app.screen

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.ui.Modifier
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.navigation
import com.flutterup.app.screen.chat.ChatRoute
import com.flutterup.app.screen.chat.ChatScreen
import com.flutterup.app.screen.discover.DiscoverRoute
import com.flutterup.app.screen.discover.DiscoverScreen
import com.flutterup.app.screen.profile.ProfileRoute
import com.flutterup.app.screen.profile.ProfileScreen
import com.flutterup.app.screen.relate.RelateRoute
import com.flutterup.app.screen.relate.RelateScreen
import kotlinx.serialization.Serializable

@Serializable data object HomeBaseRoute

fun NavGraphBuilder.homeBottomNavGraph() {
    navigation<HomeBaseRoute>(
        startDestination = DiscoverRoute::class,
    ) {
        composable(route = DiscoverRoute::class) {
            DiscoverScreen()
        }
        composable(route = RelateRoute::class) {
            RelateScreen()
        }
        composable(route = ChatRoute::class) {
            ChatScreen()
        }
        composable(route = ProfileRoute::class) {
            ProfileScreen()
        }
    }
}
