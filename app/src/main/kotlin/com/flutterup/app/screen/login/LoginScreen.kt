package com.flutterup.app.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.flutterup.app.R
import com.flutterup.app.design.component.AppRadioButton
import com.flutterup.app.design.noRippleSelectable
import com.flutterup.app.design.text.BodySize
import com.flutterup.app.design.text.BodyText
import com.flutterup.app.design.text.DisplayTitle
import com.flutterup.app.design.text.LabelText
import com.flutterup.app.design.text.buildAppAnnotatedString
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.webview.WebViewRoute

@Composable
fun LoginScreen(
    modifier: Modifier = Modifier,
    navController: NavController = LocalNavController.current,
    loginViewModel: LoginViewModel = hiltViewModel(),
) {
    var isAgreeSelected by remember { mutableStateOf(false) }

    Surface(
        modifier = modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier.fillMaxWidth()
        ) {
            Image(
                painter = painterResource(id = R.mipmap.ic_login_background),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )

            Column(
                modifier = Modifier.fillMaxSize()
                    .padding(bottom = 21.dp)
                    .navigationBarsPadding()
                    .statusBarsPadding(),
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                DisplayTitle(
                    text = stringResource(R.string.login_hello),
                    modifier = Modifier
                        .padding(start = 18.dp, top = 57.dp)
                )


                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .padding(horizontal = 24.dp)
                ) {
                    OutlinedButton(
                        onClick = {  },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                    ) {
                        BodyText(
                            text = stringResource(R.string.sign_in),
                            size = BodySize.Large
                        )
                    }

                    Spacer(modifier = Modifier.height(20.dp))

                    GoogleSignInOutlinedButton(
                        clientId = "954197137352-m5c8jc2bvpajohn8cd2ud4bqrqemgnvk.apps.googleusercontent.com",
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                    )
                }


                Row(
                    modifier = Modifier
                        .padding(horizontal = 24.dp)
                        .noRippleSelectable(
                            selected = isAgreeSelected,
                            onClick = { isAgreeSelected = !isAgreeSelected },
                            role = Role.RadioButton
                        )
                ) {
                    AppRadioButton(
                        selected = isAgreeSelected,
                        onClick = null,
                        modifier = Modifier.size(12.dp),
                    )

                    Spacer(modifier = Modifier.width(5.dp))

                    AgreementText(
                        navController = navController,
                        onClick = { isAgreeSelected = !isAgreeSelected }
                    )
                }
            }
        }
    }
}

@Composable
fun AgreementText(
    navController: NavController,
    onClick: () -> Unit = {}
) {
    val prefix = stringResource(R.string.login_agreement_prefix)
    val termsPolicy = stringResource(R.string.terms_policy)
    val privacyPolicy = stringResource(R.string.privacy_policy)
    val childSafetyPolicy = stringResource(R.string.child_safety_policy)

    val delimiter = stringResource(R.string.login_agreement_delimiter)
    val and = stringResource(R.string.login_agreement_and)


    val annotatedString = buildAppAnnotatedString {
        appendUrl(
            text = prefix,
            url = "",
            clickable = { onClick() }
        )

        appendLinkUnderline(
            text = termsPolicy,
            tag = termsPolicy,
            clickable = {
                navController.navigate(WebViewRoute(
                    title = termsPolicy,
                    url = "https://www.localuvapp.com/#/terms"
                ))
            }
        )

        append(delimiter)

        appendLinkUnderline(
            text = privacyPolicy,
            tag = privacyPolicy,
            clickable = {
                navController.navigate(WebViewRoute(
                    title = privacyPolicy,
                    url = "https://www.localuvapp.com/#/pp"
                ))
            }
        )

        append(and)

        appendLinkUnderline(
            text = childSafetyPolicy,
            tag = childSafetyPolicy,
            clickable = {
                navController.navigate(WebViewRoute(
                    title = childSafetyPolicy,
                    url = "https://www.localuvapp.com/#/child-safety-policy"
                ))
            }
        )
    }

    LabelText(
        text = annotatedString,
        maxLines = 2,
    )
}


@Preview
@Composable
private fun LoginScreenPreview() {
    AppTheme {
        LoginScreen(navController = rememberNavController())
    }
}