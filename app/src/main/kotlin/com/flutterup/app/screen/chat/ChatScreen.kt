package com.flutterup.app.screen.chat

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.flutterup.app.screen.LocalInnerPadding

@Composable
fun ChatScreen(
    modifier: Modifier = Modifier,
) {
    val innerPadding = LocalInnerPadding.current

    Box(
        modifier = modifier.fillMaxSize().padding(bottom = innerPadding.calculateBottomPadding())
    ) {
        Text(text = "Chat", modifier = Modifier.align(Alignment.Center))
    }
}