package com.flutterup.app.screen

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.navigation.NavDestination
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navOptions
import com.flutterup.app.navigation.TopLevelDestination
import com.flutterup.network.utils.NetworkMonitor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn


@Composable
fun rememberAppState(
    networkMonitor: NetworkMonitor,
    navController: NavHostController = rememberNavController(),
    coroutineScope: CoroutineScope = rememberCoroutineScope(),
): AppState {

    // We want to preserve the app state across config changes
    // We only need to initialize AppState once, and don't want to re-create it when the
    // Compose environment is recreated (e.g. due to configuration change).
    return remember(navController, coroutineScope, networkMonitor) {
        AppState(
           navController = navController,
           coroutineScope = coroutineScope,
           networkMonitor = networkMonitor
        )
    }
}

@Stable
class AppState(
    val navController: NavHostController,
    coroutineScope: CoroutineScope,
    networkMonitor: NetworkMonitor,
) {
    val currentDestination: NavDestination? @Composable get() = navController.findCurrentDestination()

    val isOnline = networkMonitor.isOnline
        .map(Boolean::not)
        .stateIn(
            coroutineScope,
            started = SharingStarted.WhileSubscribed(5000),
            initialValue = false
        )

    /**
     * Map of top level destinations to be used in the TopBar, BottomBar and NavRail. The key is the
     * route.
     */
    val topLevelDestinations: List<TopLevelDestination> = TopLevelDestination.entries

    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        val topLevelNavOptions = navOptions {
            // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
            popUpTo(navController.graph.findStartDestination().id) {
                saveState = true
            }
            // 避免重新选择同一项目时产生多个相同目标的副本
            launchSingleTop = true
            // 重新选择之前选择的项目时恢复状态
            restoreState = true
        }

        navController.navigate(topLevelDestination.route, topLevelNavOptions)
    }

    @Composable
    private fun NavHostController.findCurrentDestination(): NavDestination? {
        val navBackStackEntry by currentBackStackEntryAsState()
        return navBackStackEntry?.destination
    }
}
