package com.flutterup.app.screen

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.runtime.compositionLocalOf
import androidx.navigation.NavHostController


val LocalAppState = compositionLocalOf<AppState> {
    error("No LocaleAppState provided")
}

val LocalNavController = compositionLocalOf<NavHostController> {
    error("No LocalNavController provided")
}

val LocalInnerPadding = compositionLocalOf<PaddingValues> {
    error("No LocalInnerPadding provided")
}