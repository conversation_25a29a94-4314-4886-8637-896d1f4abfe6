package com.flutterup.app.di

import com.flutterup.app.network.ApiService
import com.flutterup.app.network.DecodeResponseInterceptor
import com.flutterup.app.network.HeaderInterceptor
import com.flutterup.app.network.NavigateResponseInterceptor
import com.flutterup.app.network.environment.AppEnvManager
import com.flutterup.app.network.environment.AppEnvironmentProvider
import com.flutterup.network.NetworkService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object NetworkServiceModule {

    @Singleton
    @Provides
    fun provideAppEnvironment(): AppEnvironmentProvider {
        return AppEnvManager.getEnvironment()
    }

    @Singleton
    @Provides
    fun provideNetworkParams(
        appEnvironmentProvider: AppEnvironmentProvider,
        headerInterceptor: HeaderInterceptor,
        decodeResponseInterceptor: DecodeResponseInterceptor,
        navigateResponseInterceptor: NavigateResponseInterceptor,
    ): NetworkService.Params {
        return NetworkService.Params(
            host = appEnvironmentProvider.host,
            connectTimeout = CONNECT_TIMEOUT,
            readTimeout = READ_TIMEOUT,
            writeTimeout = WRITE_TIMEOUT,
            retryOnConnectionFailure = false,
            interceptors = listOf(
                headerInterceptor,
                decodeResponseInterceptor,
                navigateResponseInterceptor,
            ),
        )
    }

    @Singleton
    @Provides
    fun provideApiService(networkService: NetworkService): ApiService {
        return networkService[ApiService::class.java]
    }

    private const val CONNECT_TIMEOUT = 30_000L
    private const val READ_TIMEOUT = 60_000L
    private const val WRITE_TIMEOUT = 60_000L
}