package com.flutterup.app.network

import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.Timber
import com.flutterup.network.BaseResponse
import com.squareup.moshi.Moshi
import okhttp3.Interceptor
import okhttp3.Response
import okhttp3.ResponseBody.Companion.toResponseBody
import okio.Buffer
import okio.GzipSource
import java.nio.charset.StandardCharsets
import javax.inject.Inject

class NavigateResponseInterceptor @Inject constructor(
    private val jsonUtils: JsonUtils
) : Interceptor {

    companion object {
        private const val TAG = "NavigateResponseInterceptor"
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()
        val response = chain.proceed(request)

        if (!response.isSuccessful) {
            return response
        }

        val responseBody = response.body ?: return response

        // 不修改原始响应
        val source = responseBody.source()
        source.request(Long.MAX_VALUE)
        var buffer = source.buffer.clone()

        // 处理gzip压缩的情况
        val contentEncoding = response.header("Content-Encoding")
        if (contentEncoding.equals("gzip", true)) {
            GzipSource(buffer.clone()).use { gzippedBody ->
                buffer = Buffer()
                buffer.writeAll(gzippedBody)
            }
        }

        val contentType = responseBody.contentType()
        val charset = contentType?.charset(StandardCharsets.UTF_8) ?: StandardCharsets.UTF_8
        val responseBodyString = buffer.readString(charset)

        try {
            // 解析NetworkResponse
            val networkResponse = jsonUtils.fromJson(responseBodyString, BaseResponse::class.java)

            // 如果有action字段，则使用PushManager.navigate进行跳转
            val action = networkResponse?.action
            if (action != null) {
                Timber.d(TAG, "Found action in response: id=${action.id}, params=${action.params}")

                //TODO 跳转
            }
        } catch (e: Exception) {
            // 解析失败，可能不是NetworkResponse格式
            Timber.e(TAG, "Parse NetworkResponse failed: ${e.message}")
        }

        // 重新创建ResponseBody，确保响应内容不被消费
        val newResponseBody = responseBodyString.toResponseBody(contentType)
        return response.newBuilder().body(newResponseBody).build()
    }
}