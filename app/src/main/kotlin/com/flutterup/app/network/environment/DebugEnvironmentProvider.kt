package com.flutterup.app.network.environment

import com.flutterup.base.utils.DeviceUtils
import com.flutterup.tracking.TrackingManager

class DebugNoSubAccountEnvironmentProvider() : AppEnvironmentProvider {

    override val name: String = "Dev(No sub-account)"

    override val host: String = AppEnvironmentProvider.BASE_URL_DEV

    override val adid: String = System.currentTimeMillis().toString()

    override val deviceId: String = System.currentTimeMillis().toString()
}

class DebugEnvironmentProvider() : AppEnvironmentProvider {
    override val name: String = "Dev"

    override val host: String = AppEnvironmentProvider.BASE_URL_DEV

    override val adid: String = TrackingManager.getTrackingId().orEmpty()

    override val deviceId: String = DeviceUtils.getDeviceId()
}