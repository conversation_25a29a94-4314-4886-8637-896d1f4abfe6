package com.flutterup.app.network.environment

import android.os.Parcelable
import android.os.Process.killProcess
import android.os.Process.myPid
import com.flutterup.app.BuildConfig
import com.flutterup.app.repository.UserRepository
import com.flutterup.base.BaseApplication
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.intValue
import dagger.hilt.EntryPoint
import dagger.hilt.InstallIn
import dagger.hilt.android.EntryPointAccessors
import dagger.hilt.components.SingletonComponent
import kotlinx.parcelize.Parcelize

object AppEnvManager {

    @EntryPoint
    @InstallIn(SingletonComponent::class)
    interface AppEnvEntryPoint {
        fun getMMKVStore(): MMKVStore

        fun userRepository(): UserRepository
    }

    // 懒加载获取依赖
    private val mmkvStore: MMKVStore by lazy {
        val appContext = BaseApplication.getApplicationContext()
        val entryPoint =
            EntryPointAccessors.fromApplication(appContext, AppEnvEntryPoint::class.java)
        entryPoint.getMMKVStore()
    }

    private val userRepository: UserRepository by lazy {
        val appContext = BaseApplication.getApplicationContext()
        val entryPoint =
            EntryPointAccessors.fromApplication(appContext, AppEnvEntryPoint::class.java)
        entryPoint.userRepository()
    }

    private var currentEnvironmentOrdinal by mmkvStore.intValue("currentEnvironmentOrdinal", Environment.DEV.ordinal)

    fun getEnvironment(): AppEnvironmentProvider {
        if (!BuildConfig.DEBUG) {
            return ProdEnvironmentProvider()
        }

        return when(currentEnvironmentOrdinal) {
            Environment.DEV.ordinal -> DebugNoSubAccountEnvironmentProvider()
            Environment.DEV2.ordinal -> DebugEnvironmentProvider()
            Environment.PROD.ordinal -> ProdNoSubAccountEnvironmentProvider()
            Environment.PROD2.ordinal -> ProdEnvironmentProvider()
            else -> ProdEnvironmentProvider()
        }
    }

    fun updateEnvironment(environment: Environment) {
        if (currentEnvironmentOrdinal == environment.ordinal) return //未发生变化

        currentEnvironmentOrdinal = environment.ordinal

        //退出
        userRepository.logout()
        killProcess(myPid())
    }

    @Parcelize
    enum class Environment : Parcelable {
        DEV,
        DEV2,
        PROD,
        PROD2,
    }
}