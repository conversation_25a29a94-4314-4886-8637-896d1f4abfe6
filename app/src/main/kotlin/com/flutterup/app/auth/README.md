# Google Authentication Implementation

这个包含了完整的Google登录功能实现，使用了最新的Credential Manager API。

## 组件说明

### 1. GoogleAuthManager
- 负责处理Google认证的核心逻辑
- 使用Credential Manager API进行认证
- 返回结构化的认证结果

### 2. GoogleAuthRepository
- 管理认证状态和业务逻辑
- 处理认证结果并更新用户信息
- 提供StateFlow用于UI状态观察

### 3. GoogleSignInOutlinedButton
- 可重用的Google登录按钮组件
- 支持加载状态显示
- 可自定义点击事件

## 使用方法

### 在ViewModel中使用：

```kotlin
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val googleAuthRepository: GoogleAuthRepository
) : ViewModel() {
    
    val googleAuthState: StateFlow<GoogleAuthState> = googleAuthRepository.authState
    
    fun loginWithGoogle(clientId: String) {
        viewModelScope.launch {
            googleAuthRepository.signInWithGoogle(clientId)
        }
    }
}
```

### 在Composable中使用：

```kotlin
@Composable
fun LoginScreen(loginViewModel: LoginViewModel = hiltViewModel()) {
    val googleAuthState by loginViewModel.googleAuthState.collectAsState()
    
    GoogleSignInOutlinedButton(
        clientId = "your-google-client-id",
        isLoading = googleAuthState is GoogleAuthState.Loading,
        onClick = {
            loginViewModel.loginWithGoogle("your-google-client-id")
        }
    )
    
    // 处理认证状态
    LaunchedEffect(googleAuthState) {
        when (googleAuthState) {
            is GoogleAuthState.Success -> {
                // 登录成功处理
            }
            is GoogleAuthState.Error -> {
                // 错误处理
            }
            is GoogleAuthState.Cancelled -> {
                // 取消处理
            }
            else -> {}
        }
    }
}
```

## 配置要求

1. 确保在`build.gradle.kts`中添加了必要的依赖：
   ```kotlin
   implementation(libs.googleid)
   implementation(libs.google.credentials)
   implementation(libs.google.auth)
   ```

2. 在Google Cloud Console中配置OAuth 2.0客户端ID

3. 确保应用的签名与Google Console中配置的一致

## 注意事项

- 当前实现中，认证成功后会创建一个模拟的用户信息
- 在生产环境中，应该将Google ID Token发送到后端进行验证
- 后端验证成功后返回应用的用户信息和访问令牌
- 建议添加网络错误处理和重试机制
