package com.flutterup.app.auth

import com.flutterup.app.model.UserInfo
import com.flutterup.app.repository.UserRepository
import com.flutterup.base.utils.Timber
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Google认证仓库
 * 处理Google登录的业务逻辑和状态管理
 */
@Singleton
class GoogleAuthRepository @Inject constructor(
    private val googleAuthManager: GoogleAuthManager,
    private val userRepository: UserRepository
) {
    
    companion object {
        private const val TAG = "GoogleAuthRepository"
    }
    
    private val _authState = MutableStateFlow<GoogleAuthState>(GoogleAuthState.Idle)
    val authState: StateFlow<GoogleAuthState> = _authState.asStateFlow()
    
    /**
     * 检查设备上是否有可用的Google账户
     * @param clientId Google OAuth 2.0 客户端ID
     * @return Boolean 是否有可用账户
     */
    suspend fun hasAvailableGoogleAccounts(clientId: String): Boolean {
        return try {
            googleAuthManager.hasAvailableGoogleAccounts(clientId)
        } catch (e: Exception) {
            Timber.e(TAG, "Error checking available Google accounts", e)
            false
        }
    }

    /**
     * 执行Google登录
     * @param clientId Google OAuth 2.0 客户端ID
     * @param preferExistingAccounts 是否优先使用已登录的账户
     */
    suspend fun signInWithGoogle(
        clientId: String,
        preferExistingAccounts: Boolean = false
    ) {
        try {
            _authState.value = GoogleAuthState.Loading
            Timber.d(TAG, "Starting Google authentication")
            
            when (val result = googleAuthManager.signInWithGoogle(clientId)) {
                is GoogleAuthResult.Success -> {
                    Timber.d(TAG, "Google authentication successful for user: ${result.email}")
                    
                    // TODO: 这里应该调用后端API验证Google ID Token并获取用户信息
                    // 现在先创建一个模拟的用户信息
                    val userInfo = createUserInfoFromGoogleResult(result)
                    
                    // 保存用户信息
                    userRepository.updateUserInfo(userInfo)
                    
                    _authState.value = GoogleAuthState.Success(userInfo)
                }
                
                is GoogleAuthResult.Cancelled -> {
                    Timber.d(TAG, "Google authentication cancelled by user")
                    _authState.value = GoogleAuthState.Cancelled
                }
                
                is GoogleAuthResult.Error -> {
                    Timber.e(TAG, "Google authentication failed: ${result.message}")
                    _authState.value = GoogleAuthState.Error(result.message)
                }
            }
            
        } catch (e: Exception) {
            Timber.e(TAG, "Unexpected error during Google authentication", e)
            _authState.value = GoogleAuthState.Error(e.message ?: "Unknown error occurred")
        }
    }
    
    /**
     * 重置认证状态
     */
    fun resetAuthState() {
        _authState.value = GoogleAuthState.Idle
    }
    
    /**
     * 从Google认证结果创建用户信息
     * TODO: 在实际项目中，这应该通过后端API来完成
     */
    private fun createUserInfoFromGoogleResult(result: GoogleAuthResult.Success): UserInfo {
        return UserInfo(
            userId = System.currentTimeMillis(), // 临时使用时间戳作为用户ID
            token = result.idToken, // 使用Google ID Token作为临时token
            imToken = null,
            nickname = result.displayName ?: "Google User",
            headImage = result.profilePictureUri,
            isNewUser = 1, // 假设是新用户
            isModel = false,
            sign = null,
            sex = null,
            sexuality = null,
            age = null,
            isHide = null,
            birthday = null,
            mediaList = null,
            tags = null,
            online = 1,
            right = null,
            scene = null,
            userFlag = null,
            pushConfig = null,
            location = null,
            refer = null
        )
    }
}

/**
 * Google认证状态
 */
sealed class GoogleAuthState {
    /**
     * 空闲状态
     */
    data object Idle : GoogleAuthState()
    
    /**
     * 加载中
     */
    data object Loading : GoogleAuthState()
    
    /**
     * 认证成功
     */
    data class Success(val userInfo: UserInfo) : GoogleAuthState()
    
    /**
     * 用户取消认证
     */
    data object Cancelled : GoogleAuthState()
    
    /**
     * 认证失败
     */
    data class Error(val message: String) : GoogleAuthState()
}
