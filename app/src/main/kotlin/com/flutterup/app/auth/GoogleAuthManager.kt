package com.flutterup.app.auth

import android.content.Context
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.exceptions.GetCredentialCancellationException
import androidx.credentials.exceptions.GetCredentialException
import androidx.credentials.exceptions.NoCredentialException
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.flutterup.base.utils.Timber
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Google认证管理器
 * 使用新的Credential Manager API进行Google登录
 */
@Singleton
class GoogleAuthManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "GoogleAuthManager"
    }
    
    private val credentialManager = CredentialManager.create(context)
    
    /**
     * 执行Google登录
     * @param clientId Google OAuth 2.0 客户端ID
     * @param filterByAuthorizedAccounts 是否只显示已授权的账户
     * @param autoSelectEnabled 是否自动选择账户（如果只有一个账户）
     * @return GoogleAuthResult 认证结果
     */
    suspend fun signInWithGoogle(
        clientId: String,
        filterByAuthorizedAccounts: Boolean = false,
        autoSelectEnabled: Boolean = false
    ): GoogleAuthResult {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d(TAG, "Starting Google sign-in process")
                
                // 创建Google ID选项
                val googleIdOption = GetGoogleIdOption.Builder()
                    .setServerClientId(clientId)
                    .setFilterByAuthorizedAccounts(false) // 允许选择任何Google账户
                    .setAutoSelectEnabled(false) // 不自动选择账户
                    .build()
                
                // 创建凭据请求
                val request = GetCredentialRequest.Builder()
                    .addCredentialOption(googleIdOption)
                    .build()
                
                // 获取凭据
                val result = credentialManager.getCredential(
                    context = context,
                    request = request
                )
                
                // 处理凭据
                when (val credential = result.credential) {
                    is GoogleIdTokenCredential -> {
                        Timber.d(TAG, "Google sign-in successful")
                        GoogleAuthResult.Success(
                            idToken = credential.idToken,
                            displayName = credential.displayName,
                            email = credential.id,
                            profilePictureUri = credential.profilePictureUri?.toString()
                        )
                    }
                    else -> {
                        Timber.e(TAG, "Unexpected credential type: ${credential::class.java}")
                        GoogleAuthResult.Error("Unexpected credential type")
                    }
                }
                
            } catch (e: GetCredentialException) {
                Timber.e(TAG, "Google sign-in failed", e)
                when (e) {
                    is GetCredentialCancellationException -> {
                        GoogleAuthResult.Cancelled
                    }
                    is NoCredentialException -> {
                        GoogleAuthResult.Error("No Google account found")
                    }
                    else -> {
                        GoogleAuthResult.Error(e.message ?: "Unknown error occurred")
                    }
                }
            } catch (e: Exception) {
                Timber.e(TAG, "Unexpected error during Google sign-in", e)
                GoogleAuthResult.Error(e.message ?: "Unexpected error occurred")
            }
        }
    }
}

/**
 * Google认证结果
 */
sealed class GoogleAuthResult {
    /**
     * 认证成功
     */
    data class Success(
        val idToken: String,
        val displayName: String?,
        val email: String,
        val profilePictureUri: String?
    ) : GoogleAuthResult()
    
    /**
     * 用户取消认证
     */
    data object Cancelled : GoogleAuthResult()
    
    /**
     * 认证失败
     */
    data class Error(val message: String) : GoogleAuthResult()
}
