# FlutterUp 文字设计系统

## 概述

FlutterUp 文字设计系统基于 Material Design 3 的 Typography 系统，提供了一套统一、一致的文字组件和样式，确保整个应用的文字展示保持一致性和可维护性。

## 核心组件

### 1. AppText - 基础文字组件

`AppText` 是所有文字组件的基础，封装了 Compose 的 `Text` 组件，并添加了选择性文字支持。

```kotlin
AppText(
    text = "示例文字",
    color = MaterialTheme.colorScheme.onSurface,
    style = MaterialTheme.typography.bodyMedium,
    selectable = true // 支持文字选择
)
```

### 2. AppAnnotatedText - 富文本组件

`AppAnnotatedText` 支持 `AnnotatedString`，可以在单个文本中使用多种样式、颜色和交互。

```kotlin
AppAnnotatedText(
    text = annotatedString,
    onClick = { offset ->
        // 处理点击事件
    },
    selectable = true
)
```

### 3. 语义化文字组件

#### DisplayText - 显示级文字
用于最重要的标题，如应用名称、主要页面标题等。

```kotlin
DisplayText(
    text = "FlutterUp",
    size = DisplaySize.Large
)
```

#### HeadlineText - 标题文字
用于页面标题、章节标题等。

```kotlin
HeadlineText(
    text = "用户设置",
    size = HeadlineSize.Medium
)
```

#### TitleText - 组件标题
用于卡片标题、对话框标题等。

```kotlin
TitleText(
    text = "个人信息",
    size = TitleSize.Medium
)
```

#### BodyText - 正文文字
用于内容文本、描述文字等。

```kotlin
BodyText(
    text = "这是一段正文内容...",
    size = BodySize.Medium
)
```

#### LabelText - 标签文字
用于按钮、导航、标签等。

```kotlin
LabelText(
    text = "确认",
    size = LabelSize.Large
)
```

### 3. 特殊用途组件

#### PageTitle - 页面标题
```kotlin
PageTitle("设置")
```

#### CardTitle - 卡片标题
```kotlin
CardTitle("用户信息")
```

#### CardSubtitle - 卡片副标题
```kotlin
CardSubtitle("管理您的个人资料")
```

#### ButtonText - 按钮文字
```kotlin
ButtonText("登录")
```

#### NavigationText - 导航文字
```kotlin
NavigationText(
    text = "首页",
    selected = true
)
```

#### TagText - 标签文字
```kotlin
TagText("新功能")
```

#### ErrorText - 错误提示
```kotlin
ErrorText("用户名不能为空")
```

#### HintText - 提示文字
```kotlin
HintText("请输入6-20位密码")
```

#### PlaceholderText - 占位符文字
```kotlin
PlaceholderText("请输入邮箱")
```

#### EmphasisText - 强调文字
```kotlin
EmphasisText("重要提醒")
```

#### LinkText - 链接文字
```kotlin
LinkText("忘记密码？")
```

#### NumberText - 数字显示
```kotlin
NumberText(
    text = "1,234",
    size = NumberSize.Large
)
```

## AnnotatedString 支持

### 1. 构建器模式

使用 `buildAppAnnotatedString` 创建富文本：

```kotlin
val richText = buildAppAnnotatedString {
    append("这是普通文字，")
    appendBold("这是粗体，")
    appendLink("这是链接", "https://example.com")
    appendError("这是错误文字")
}

BodyText(
    text = richText,
    onClick = { offset ->
        // 处理点击事件
    }
)
```

### 2. 主题化构建器

使用 `buildThemedAnnotatedString` 创建主题化富文本：

```kotlin
val themedText = buildThemedAnnotatedString {
    append("欢迎使用 ")
    appendPrimary("FlutterUp")
    append("！")
    appendSecondary("这是副标题文字")
}
```

### 3. 自动链接检测

```kotlin
val autoLinkedText = AnnotatedStringUtils.autoLinkText(
    "访问 https://example.com 了解更多信息"
)

val autoEmailText = AnnotatedStringUtils.autoEmailText(
    "联系我们：<EMAIL>"
)
```

### 4. 点击事件处理

```kotlin
BodyText(
    text = annotatedString,
    onClick = { offset ->
        AnnotatedStringUtils.handleClick(
            annotatedString = annotatedString,
            offset = offset,
            onUrlClick = { url -> /* 处理URL点击 */ },
            onEmailClick = { email -> /* 处理邮箱点击 */ },
            onUserClick = { userId -> /* 处理用户点击 */ }
        )
    }
)
```

### 5. AnnotatedString 版本的组件

所有语义化组件都支持 AnnotatedString：

```kotlin
// 页面标题
PageTitle(
    text = buildAppAnnotatedString {
        append("欢迎使用 ")
        appendEmphasis("FlutterUp")
    }
)

// 卡片标题
CardTitle(
    text = buildAppAnnotatedString {
        appendBold("重要通知")
        append("：系统维护")
    }
)

// 错误提示
ErrorText(
    text = buildAppAnnotatedString {
        appendError("错误：")
        append("用户名不能为空")
    }
)
```

## 样式系统

### 1. 文字颜色 (TextColors)

```kotlin
// 主要颜色
TextColors.primary()
TextColors.secondary()

// 表面颜色
TextColors.onSurface()
TextColors.onSurfaceVariant()

// 状态颜色
TextColors.error()
TextColors.success()
TextColors.warning()
TextColors.info()

// 特殊状态
TextColors.disabled()
TextColors.placeholder()
```

### 2. 文字权重 (TextWeights)

```kotlin
TextWeights.Light
TextWeights.Normal
TextWeights.Medium
TextWeights.SemiBold
TextWeights.Bold
```

### 3. 文字大小 (TextSizes)

```kotlin
TextSizes.Small      // 12.sp
TextSizes.Medium     // 14.sp
TextSizes.Large      // 16.sp
TextSizes.Title      // 22.sp
TextSizes.Headline   // 24.sp
```

### 4. 样式预设 (TextPresets)

```kotlin
// 页面相关
TextPresets.pageTitle()
TextPresets.pageSubtitle()

// 卡片相关
TextPresets.cardTitle()
TextPresets.cardContent()

// 交互相关
TextPresets.button()
TextPresets.link()

// 表单相关
TextPresets.inputLabel()
TextPresets.inputHint()
TextPresets.errorMessage()
```

## 使用指南

### 1. 选择合适的组件

- **页面标题**: 使用 `PageTitle` 或 `HeadlineText`
- **卡片标题**: 使用 `CardTitle` 或 `TitleText`
- **正文内容**: 使用 `BodyText`
- **按钮文字**: 使用 `ButtonText` 或 `LabelText`
- **错误提示**: 使用 `ErrorText`
- **链接文字**: 使用 `LinkText`

### 2. 保持一致性

- 同类型的文字使用相同的组件
- 遵循设计系统的层级关系
- 使用预设的颜色和样式

### 3. 响应式设计

- 考虑不同屏幕尺寸下的文字显示
- 合理设置 `maxLines` 和 `overflow`
- 使用 `selectable` 属性支持长文本选择

### 4. 可访问性

- 确保文字颜色对比度符合要求
- 为重要信息提供足够的文字大小
- 考虑使用语义化的组件名称

## 最佳实践

### 1. 导入方式

```kotlin
import com.flutterup.app.design.text.*
```

### 2. 常见用法示例

```kotlin
@Composable
fun UserProfileCard() {
    Card {
        Column(modifier = Modifier.padding(16.dp)) {
            // 卡片标题
            CardTitle("用户信息")
            
            // 用户名
            BodyText(
                text = "张三",
                style = TextPresets.cardContent()
            )
            
            // 邮箱
            BodyText(
                text = "<EMAIL>",
                color = TextColors.onSurfaceVariant()
            )
            
            // 编辑链接
            LinkText("编辑资料")
        }
    }
}
```

### 3. 错误处理

```kotlin
@Composable
fun LoginForm() {
    Column {
        // 输入框标签
        AppText(
            text = "邮箱",
            style = TextPresets.inputLabel()
        )

        // 错误提示
        if (hasError) {
            ErrorText("请输入有效的邮箱地址")
        }
    }
}
```

### 4. AnnotatedString 使用

```kotlin
@Composable
fun RichTextExample() {
    val richText = buildAppAnnotatedString {
        append("请阅读 ")
        appendLink("用户协议", "https://example.com/terms")
        append(" 和 ")
        appendLink("隐私政策", "https://example.com/privacy")
        append("。")
        appendLine()
        appendError("注意：违反协议将导致账号被封禁。")
    }

    BodyText(
        text = richText,
        onClick = { offset ->
            AnnotatedStringUtils.handleClick(
                annotatedString = richText,
                offset = offset,
                onUrlClick = { url ->
                    // 打开链接
                }
            )
        }
    )
}
```

## 注意事项

1. **避免直接使用 Material3 的 Text 组件**，优先使用设计系统提供的组件
2. **保持样式一致性**，不要随意修改预设的样式
3. **考虑国际化**，确保文字组件支持多语言
4. **性能优化**，避免在列表中使用复杂的文字样式
5. **测试覆盖**，确保文字在不同主题下都能正常显示
6. **AnnotatedString 性能**，避免在频繁重组的地方创建复杂的 AnnotatedString
7. **点击事件处理**，确保正确处理 AnnotatedString 的点击事件和注解

## 扩展指南

如需添加新的文字组件或样式：

1. 在 `TextComponents.kt` 中添加新的组件
2. 在 `TextExtensions.kt` 中添加新的样式预设
3. 在 `TextPreviews.kt` 中添加预览
4. 更新此文档的使用说明

## 迁移指南

### 从 Material3 Text 迁移

#### 旧代码
```kotlin
Text(
    text = "标题",
    style = MaterialTheme.typography.headlineMedium,
    color = MaterialTheme.colorScheme.onSurface
)
```

#### 新代码
```kotlin
HeadlineText(
    text = "标题",
    size = HeadlineSize.Medium
)
```

### 常见迁移场景

| 旧用法                                                       | 新用法                                       |
|-----------------------------------------------------------|-------------------------------------------|
| `Text("标题", style = MaterialTheme.typography.titleLarge)` | `TitleText("标题", size = TitleSize.Large)` |
| `Text("内容", style = MaterialTheme.typography.bodyMedium)` | `BodyText("内容", size = BodySize.Medium)`  |
| `Text("按钮", style = MaterialTheme.typography.labelLarge)` | `ButtonText("按钮")`                        |
| `Text("错误", color = MaterialTheme.colorScheme.error)`     | `ErrorText("错误")`                         |

### 批量替换建议

1. 使用 IDE 的查找替换功能
2. 逐步迁移，从新功能开始
3. 保持向后兼容性
4. 添加单元测试验证

## 版本历史

- v1.0.0: 初始版本，包含基础文字组件和样式系统
- v1.1.0: 新增 AnnotatedString 支持
  - 添加 `AppAnnotatedText` 组件
  - 所有语义化组件支持 AnnotatedString 重载
  - 新增 `AppAnnotatedStringBuilder` 构建器
  - 新增 `buildAppAnnotatedString` 和 `buildThemedAnnotatedString` DSL
  - 新增 `AnnotatedStringUtils` 工具类
  - 支持自动链接检测和点击事件处理
  - 添加丰富的使用示例和预览
