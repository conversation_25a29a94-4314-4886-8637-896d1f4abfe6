package com.flutterup.app.design.component

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButtonColors
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.preview.BooleanProvider

@Composable
fun AppRadioButton(
    selected: <PERSON><PERSON>an,
    onClick: (() -> Unit)?,
    modifier: Modifier = Modifier,
    size: Dp = 16.dp,
    strokeWidth: Dp = 1.dp,
    colors: RadioButtonColors = colors()
) {
    ScalableRadioButton(
        selected = selected,
        onClick = onClick,
        modifier = modifier,
        size = size,
        strokeWidth = strokeWidth,
        selectedColor = colors.selectedColor,
        unselectedColor = colors.unselectedColor
    )
}

// 7. 可缩放尺寸的 RadioButton
@Composable
fun ScalableRadioButton(
    selected: Boolean,
    onClick: (() -> Unit)?,
    enabled: Boolean = true,
    modifier: Modifier = Modifier,
    size: Dp = 16.dp,
    strokeWidth: Dp = 1.dp,
    selectedColor: Color = MaterialTheme.colorScheme.primary,
    unselectedColor: Color = MaterialTheme.colorScheme.outline,
    interactionSource: MutableInteractionSource? = null
) {
    val borderColor = if (selected) selectedColor else unselectedColor
    val centerColor = if (selected) selectedColor else Color.Transparent

    val selectableModifier =
        if (onClick != null) {
            modifier.selectable(
                selected = selected,
                onClick = onClick,
                enabled = enabled,
                role = Role.RadioButton,
                interactionSource = interactionSource,
                indication = ripple(bounded = false, radius = 40.dp / 2)
            )
        } else {
            modifier
        }

    Box(
        modifier = selectableModifier
            .size(size)
            .clip(CircleShape)
            .border(strokeWidth, borderColor, CircleShape)
            .background(Color.Transparent),
        contentAlignment = Alignment.Center
    ) {
        if (selected) {
            Box(
                modifier = Modifier
                    .size(size / 2)
                    .background(centerColor, CircleShape)
            )
        }
    }
}

@Composable
private fun colors(
    selectedColor: Color = MaterialTheme.colorScheme.primary,
    unselectedColor: Color = MaterialTheme.colorScheme.outline,
    disabledSelectedColor: Color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
    disabledUnselectedColor: Color = MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
): RadioButtonColors {
    return RadioButtonDefaults.colors(
        selectedColor = selectedColor,
        unselectedColor = unselectedColor,
        disabledSelectedColor = disabledSelectedColor,
        disabledUnselectedColor = disabledUnselectedColor
    )
}

@Preview(showBackground = true)
@Composable
private fun AppRadioButtonPreview(
    @PreviewParameter(BooleanProvider::class) selected: Boolean
) {
    AppRadioButton(
        selected = selected,
        onClick = {},
    )
}

private const val RadioAnimationDuration = 100

private val RadioButtonPadding = 2.dp
private val RadioButtonDotSize = 12.dp
private val RadioStrokeWidth = 2.dp
