package com.flutterup.app.design.text

import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp

/**
 * 超大标题文字
 */
@Composable
fun DisplayTitle(
    text: String,
    modifier: Modifier = Modifier,
    size: DisplaySize = DisplaySize.Large,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign = TextAlign.Start,
) {
    DisplayText(
        text = text,
        modifier = modifier,
        size = size,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 页面标题组件
 */
@Composable
fun PageTitle(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign = TextAlign.Start,
) {
    HeadlineText(
        text = text,
        modifier = modifier,
        size = HeadlineSize.Large,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 页面标题组件 - AnnotatedString 版本
 */
@Composable
fun PageTitle(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    HeadlineText(
        text = text,
        modifier = modifier,
        size = HeadlineSize.Large,
        color = color,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 卡片标题组件
 */
@Composable
fun CardTitle(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign = TextAlign.Start,
) {
    TitleText(
        text = text,
        modifier = modifier,
        size = TitleSize.Medium,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 卡片副标题组件
 */
@Composable
fun CardSubtitle(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textAlign: TextAlign = TextAlign.Start,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Medium,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 按钮文字组件
 */
@Composable
fun ButtonText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onPrimary,
    textAlign: TextAlign = TextAlign.Center,
) {
    LabelText(
        text = text,
        modifier = modifier,
        size = LabelSize.Large,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 导航文字组件
 */
@Composable
fun NavigationText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    selected: Boolean = false,
    textAlign: TextAlign = TextAlign.Center,
) {
    val textColor = if (selected) {
        MaterialTheme.colorScheme.primary
    } else {
        color
    }

    LabelText(
        text = text,
        modifier = modifier,
        size = LabelSize.Medium,
        color = textColor,
        textAlign = textAlign,
    )
}

/**
 * 标签文字组件
 */
@Composable
fun TagText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSecondaryContainer,
    textAlign: TextAlign = TextAlign.Center,
) {
    LabelText(
        text = text,
        modifier = modifier.padding(horizontal = 8.dp, vertical = 4.dp),
        size = LabelSize.Small,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 错误文字组件
 */
@Composable
fun ErrorText(
    text: String,
    modifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Start,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Small,
        color = MaterialTheme.colorScheme.error,
        textAlign = textAlign,
    )
}

/**
 * 提示文字组件
 */
@Composable
fun HintText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textAlign: TextAlign = TextAlign.Start,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Small,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 占位符文字组件
 */
@Composable
fun PlaceholderText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
    textAlign: TextAlign = TextAlign.Start,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Medium,
        color = color,
        textAlign = textAlign,
    )
}

/**
 * 强调文字组件
 */
@Composable
fun EmphasisText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    textAlign: TextAlign = TextAlign.Start,
) {
    AppText(
        text = text,
        modifier = modifier,
        color = color,
        fontWeight = FontWeight.SemiBold,
        textAlign = textAlign,
        style = MaterialTheme.typography.bodyMedium,
        overflow = TextOverflow.Ellipsis
    )
}

/**
 * 链接文字组件
 */
@Composable
fun LinkText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    textAlign: TextAlign = TextAlign.Start,
) {
    AppText(
        text = text,
        modifier = modifier,
        color = color,
        fontWeight = FontWeight.Medium,
        textAlign = textAlign,
        style = MaterialTheme.typography.bodyMedium,
        overflow = TextOverflow.Ellipsis
    )
}

/**
 * 数字显示组件
 */
@Composable
fun NumberText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurface,
    size: NumberSize = NumberSize.Medium,
    textAlign: TextAlign = TextAlign.Center,
) {
    val style = when (size) {
        NumberSize.Large -> MaterialTheme.typography.headlineLarge
        NumberSize.Medium -> MaterialTheme.typography.titleLarge
        NumberSize.Small -> MaterialTheme.typography.titleMedium
    }

    AppText(
        text = text,
        modifier = modifier,
        color = color,
        fontWeight = FontWeight.Bold,
        textAlign = textAlign,
        style = style,
        overflow = TextOverflow.Ellipsis
    )
}

enum class NumberSize { Large, Medium, Small }

// AnnotatedString 版本的组件

/**
 * 卡片标题组件 - AnnotatedString 版本
 */
@Composable
fun CardTitle(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    TitleText(
        text = text,
        modifier = modifier,
        size = TitleSize.Medium,
        color = color,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 卡片副标题组件 - AnnotatedString 版本
 */
@Composable
fun CardSubtitle(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Medium,
        color = color,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 按钮文字组件 - AnnotatedString 版本
 */
@Composable
fun ButtonText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onPrimary,
    textAlign: TextAlign = TextAlign.Center,
    onClick: ((Int) -> Unit)? = null,
) {
    LabelText(
        text = text,
        modifier = modifier,
        size = LabelSize.Large,
        color = color,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 导航文字组件 - AnnotatedString 版本
 */
@Composable
fun NavigationText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    selected: Boolean = false,
    textAlign: TextAlign = TextAlign.Center,
    onClick: ((Int) -> Unit)? = null,
) {
    val textColor = if (selected) {
        MaterialTheme.colorScheme.primary
    } else {
        color
    }

    LabelText(
        text = text,
        modifier = modifier,
        size = LabelSize.Medium,
        color = textColor,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 错误文字组件 - AnnotatedString 版本
 */
@Composable
fun ErrorText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Small,
        color = MaterialTheme.colorScheme.error,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 提示文字组件 - AnnotatedString 版本
 */
@Composable
fun HintText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onSurfaceVariant,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    BodyText(
        text = text,
        modifier = modifier,
        size = BodySize.Small,
        color = color,
        textAlign = textAlign,
        onClick = onClick
    )
}

/**
 * 强调文字组件 - AnnotatedString 版本
 */
@Composable
fun EmphasisText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        fontWeight = FontWeight.SemiBold,
        textAlign = textAlign,
        style = MaterialTheme.typography.bodyMedium,
        overflow = TextOverflow.Ellipsis,
        onClick = onClick
    )
}

/**
 * 链接文字组件 - AnnotatedString 版本
 */
@Composable
fun LinkText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.primary,
    textAlign: TextAlign = TextAlign.Start,
    onClick: ((Int) -> Unit)? = null,
) {
    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        fontWeight = FontWeight.Medium,
        textAlign = textAlign,
        style = MaterialTheme.typography.bodyMedium,
        overflow = TextOverflow.Ellipsis,
        onClick = onClick
    )
}
