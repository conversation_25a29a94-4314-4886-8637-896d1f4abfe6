package com.flutterup.app.design.text

import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.text.selection.SelectionContainer
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit

/**
 * 应用统一的文字组件
 * 基于Material3 Typography系统，提供一致的文字样式
 */
@Composable
fun AppText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    style: TextStyle = LocalTextStyle.current,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    selectable: Boolean = false,
) {
    val content = @Composable {
        Text(
            text = text,
            modifier = modifier,
            color = color,
            fontSize = fontSize,
            fontStyle = fontStyle,
            fontWeight = fontWeight,
            fontFamily = fontFamily,
            letterSpacing = letterSpacing,
            textDecoration = textDecoration,
            textAlign = textAlign,
            lineHeight = lineHeight,
            overflow = overflow,
            softWrap = softWrap,
            maxLines = maxLines,
            minLines = minLines,
            onTextLayout = onTextLayout,
            style = style
        )
    }

    if (selectable) {
        SelectionContainer {
            content()
        }
    } else {
        content()
    }
}

/**
 * 支持 AnnotatedString 的基础文字组件
 */
@Composable
fun AppAnnotatedText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    style: TextStyle = LocalTextStyle.current,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    onClick: ((Int) -> Unit)? = null,
    selectable: Boolean = false,
) {
    val content = @Composable {
        if (onClick != null) {
            ClickableText(
                text = text,
                modifier = modifier,
                style = style.copy(
                    color = if (color != Color.Unspecified) color else style.color,
                    fontSize = if (fontSize != TextUnit.Unspecified) fontSize else style.fontSize,
                    fontStyle = fontStyle ?: style.fontStyle,
                    fontWeight = fontWeight ?: style.fontWeight,
                    fontFamily = fontFamily ?: style.fontFamily,
                    letterSpacing = if (letterSpacing != TextUnit.Unspecified) letterSpacing else style.letterSpacing,
                    textDecoration = textDecoration ?: style.textDecoration,
                    textAlign = textAlign ?: style.textAlign,
                    lineHeight = if (lineHeight != TextUnit.Unspecified) lineHeight else style.lineHeight
                ),
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                onTextLayout = onTextLayout,
                onClick = onClick
            )
        } else {
            Text(
                text = text,
                modifier = modifier,
                color = color,
                fontSize = fontSize,
                fontStyle = fontStyle,
                fontWeight = fontWeight,
                fontFamily = fontFamily,
                letterSpacing = letterSpacing,
                textDecoration = textDecoration,
                textAlign = textAlign,
                lineHeight = lineHeight,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                minLines = minLines,
                onTextLayout = onTextLayout,
                style = style
            )
        }
    }

    if (selectable) {
        SelectionContainer {
            content()
        }
    } else {
        content()
    }
}

/**
 * 显示级别文字 - 用于最重要的标题
 */
@Composable
fun DisplayText(
    text: String,
    modifier: Modifier = Modifier,
    size: DisplaySize = DisplaySize.Large,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    selectable: Boolean = false,
) {
    val style = when (size) {
        DisplaySize.Large -> MaterialTheme.typography.displayLarge
        DisplaySize.Medium -> MaterialTheme.typography.displayMedium
        DisplaySize.Small -> MaterialTheme.typography.displaySmall
    }

    AppText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        selectable = selectable
    )
}

/**
 * 显示级别文字 - AnnotatedString 版本
 */
@Composable
fun DisplayText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    size: DisplaySize = DisplaySize.Large,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    onClick: ((Int) -> Unit)? = null,
    selectable: Boolean = false,
) {
    val style = when (size) {
        DisplaySize.Large -> MaterialTheme.typography.displayLarge
        DisplaySize.Medium -> MaterialTheme.typography.displayMedium
        DisplaySize.Small -> MaterialTheme.typography.displaySmall
    }

    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        onClick = onClick,
        selectable = selectable
    )
}

/**
 * 标题文字 - 用于页面标题、卡片标题等
 */
@Composable
fun HeadlineText(
    text: String,
    modifier: Modifier = Modifier,
    size: HeadlineSize = HeadlineSize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    selectable: Boolean = false,
) {
    val style = when (size) {
        HeadlineSize.Large -> MaterialTheme.typography.headlineLarge
        HeadlineSize.Medium -> MaterialTheme.typography.headlineMedium
        HeadlineSize.Small -> MaterialTheme.typography.headlineSmall
    }

    AppText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        selectable = selectable
    )
}

/**
 * 标题文字 - AnnotatedString 版本
 */
@Composable
fun HeadlineText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    size: HeadlineSize = HeadlineSize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    onClick: ((Int) -> Unit)? = null,
    selectable: Boolean = false,
) {
    val style = when (size) {
        HeadlineSize.Large -> MaterialTheme.typography.headlineLarge
        HeadlineSize.Medium -> MaterialTheme.typography.headlineMedium
        HeadlineSize.Small -> MaterialTheme.typography.headlineSmall
    }

    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        onClick = onClick,
        selectable = selectable
    )
}

/**
 * 标题文字 - 用于组件标题
 */
@Composable
fun TitleText(
    text: String,
    modifier: Modifier = Modifier,
    size: TitleSize = TitleSize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    selectable: Boolean = false,
) {
    val style = when (size) {
        TitleSize.Large -> MaterialTheme.typography.titleLarge
        TitleSize.Medium -> MaterialTheme.typography.titleMedium
        TitleSize.Small -> MaterialTheme.typography.titleSmall
    }

    AppText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        selectable = selectable
    )
}

/**
 * 标题文字 - AnnotatedString 版本
 */
@Composable
fun TitleText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    size: TitleSize = TitleSize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    onClick: ((Int) -> Unit)? = null,
    selectable: Boolean = false,
) {
    val style = when (size) {
        TitleSize.Large -> MaterialTheme.typography.titleLarge
        TitleSize.Medium -> MaterialTheme.typography.titleMedium
        TitleSize.Small -> MaterialTheme.typography.titleSmall
    }

    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        onClick = onClick,
        selectable = selectable
    )
}

/**
 * 正文文字 - 用于内容文本
 */
@Composable
fun BodyText(
    text: String,
    modifier: Modifier = Modifier,
    size: BodySize = BodySize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    selectable: Boolean = false,
) {
    val style = when (size) {
        BodySize.Large -> MaterialTheme.typography.bodyLarge
        BodySize.Medium -> MaterialTheme.typography.bodyMedium
        BodySize.Small -> MaterialTheme.typography.bodySmall
    }

    AppText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        selectable = selectable
    )
}

/**
 * 正文文字 - AnnotatedString 版本
 */
@Composable
fun BodyText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    size: BodySize = BodySize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    onClick: ((Int) -> Unit)? = null,
    selectable: Boolean = false,
) {
    val style = when (size) {
        BodySize.Large -> MaterialTheme.typography.bodyLarge
        BodySize.Medium -> MaterialTheme.typography.bodyMedium
        BodySize.Small -> MaterialTheme.typography.bodySmall
    }

    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        onClick = onClick,
        selectable = selectable
    )
}

/**
 * 标签文字 - 用于按钮、导航、标签等
 */
@Composable
fun LabelText(
    text: String,
    modifier: Modifier = Modifier,
    size: LabelSize = LabelSize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    selectable: Boolean = false,
) {
    val style = when (size) {
        LabelSize.Large -> MaterialTheme.typography.labelLarge
        LabelSize.Medium -> MaterialTheme.typography.labelMedium
        LabelSize.Small -> MaterialTheme.typography.labelSmall
    }

    AppText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        selectable = selectable
    )
}

/**
 * 标签文字 - AnnotatedString 版本
 */
@Composable
fun LabelText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    size: LabelSize = LabelSize.Medium,
    color: Color = MaterialTheme.colorScheme.onSurface,
    textAlign: TextAlign? = null,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Ellipsis,
    onClick: ((Int) -> Unit)? = null,
    selectable: Boolean = false,
) {
    val style = when (size) {
        LabelSize.Large -> MaterialTheme.typography.labelLarge
        LabelSize.Medium -> MaterialTheme.typography.labelMedium
        LabelSize.Small -> MaterialTheme.typography.labelSmall
    }

    AppAnnotatedText(
        text = text,
        modifier = modifier,
        color = color,
        textAlign = textAlign,
        maxLines = maxLines,
        overflow = overflow,
        style = style,
        onClick = onClick,
        selectable = selectable
    )
}

// 尺寸枚举
enum class DisplaySize { Large, Medium, Small }
enum class HeadlineSize { Large, Medium, Small }
enum class TitleSize { Large, Medium, Small }
enum class BodySize { Large, Medium, Small }
enum class LabelSize { Large, Medium, Small }
