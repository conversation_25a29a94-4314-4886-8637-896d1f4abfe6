package com.flutterup.app.navigation

import androidx.compose.material.BottomNavigation
import androidx.compose.material.BottomNavigationItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavDestination.Companion.hierarchy
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.toRoute
import com.flutterup.app.screen.AppState
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.MainActivityUIState
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.LocalInnerPadding
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.homeBottomNavGraph
import com.flutterup.app.screen.login.LoginBaseRoute
import com.flutterup.app.screen.login.loginGraph
import com.flutterup.app.screen.webview.CustomWebViewScreen
import com.flutterup.app.screen.webview.WebViewRoute
import com.flutterup.app.screen.webview.webViewScreen
import kotlin.reflect.KClass

@Composable
fun AppScreen(
    uiState: MainActivityUIState,
    modifier: Modifier = Modifier,
) {
    val appState = LocalAppState.current
    LocalNavController.current

    Scaffold(
        bottomBar = {
            if (appState.currentDestination.isRouteInBottomNav()) {
                BottomNavigation {
                    appState.topLevelDestinations.forEach { destination ->
                        val selected = appState.currentDestination.isRouteInHierarchy(destination.routeType)

                        BottomNavigationItem(
                            selected = selected,
                            onClick = { appState.navigateToTopLevelDestination(destination) },
                            icon = {
                                val icon = if (selected) destination.selectedIcon else destination.unselectedIcon
                                Icon(icon, contentDescription = null)
                            },
                            alwaysShowLabel = false
                        )
                    }
                }
            }
        },
        modifier = modifier
    ) { innerPadding ->
        CompositionLocalProvider(
            LocalInnerPadding provides innerPadding,
        ) {
            FlutterUpNavHost(
                appState = appState,
                uiState = uiState,
            )
        }
    }
}

@Composable
fun FlutterUpNavHost(
    appState: AppState,
    uiState: MainActivityUIState,
    modifier: Modifier = Modifier,
) {
    val navController = appState.navController

    if (uiState.shouldKeepSplashScreen()) {
        return
    }

    val startDestination = when (uiState) {
        is MainActivityUIState.Success -> HomeBaseRoute::class
        else -> LoginBaseRoute::class
    }

    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier,
        enterTransition = { NavigationAnimations.Presets.horizontalSlide.enter },
        exitTransition = { NavigationAnimations.Presets.horizontalSlide.exit },
        popEnterTransition = { NavigationAnimations.Presets.horizontalSlide.popEnter },
        popExitTransition = { NavigationAnimations.Presets.horizontalSlide.popExit }
    ) {
        loginGraph()

        homeBottomNavGraph()

        webViewScreen()
    }
}


private fun NavDestination?.isRouteInHierarchy(route: KClass<*>) =
    this?.hierarchy?.any {
        it.hasRoute(route)
    } ?: false

private fun NavDestination?.isRouteInBottomNav() =
    TopLevelDestination.entries.any { destination ->
        this?.hierarchy?.any { it.hasRoute(destination.routeType) } ?: false
    }
