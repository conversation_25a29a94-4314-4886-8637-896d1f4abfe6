package com.flutterup.app.navigation

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AccountCircle
import androidx.compose.material.icons.rounded.Check
import androidx.compose.material.icons.rounded.CheckCircle
import androidx.compose.material.icons.rounded.Email
import androidx.compose.material.icons.rounded.Favorite
import androidx.compose.material.icons.rounded.FavoriteBorder
import androidx.compose.material.icons.rounded.MailOutline
import androidx.compose.material.icons.rounded.Person
import androidx.compose.ui.graphics.vector.ImageVector
import com.flutterup.app.screen.chat.ChatRoute
import com.flutterup.app.screen.discover.DiscoverRoute
import com.flutterup.app.screen.profile.ProfileRoute
import com.flutterup.app.screen.relate.RelateRoute
import kotlin.math.round
import kotlin.reflect.KClass


/**
 * Bottom navigation routes
 */
interface BottomNavRoute

/**
 * Type for the top level destinations in the application. Contains metadata about the destination
 * that is used in the top app bar and common navigation UI.
 *
 * @param selectedIcon The icon to be displayed in the navigation UI when this destination is
 * selected.
 * @param unselectedIcon The icon to be displayed in the navigation UI when this destination is
 * not selected.
 * @param routeType The route to use when navigating to this destination.
 * there is a single destination in that section of the app (no nested destinations).
 */
enum class TopLevelDestination(
    val selectedIcon: ImageVector,
    val unselectedIcon: ImageVector,
    val route: BottomNavRoute,
    val routeType: KClass<*>,
) {
    DISCOVER(
        selectedIcon = Icons.Rounded.CheckCircle,
        unselectedIcon = Icons.Rounded.Check,
        route = DiscoverRoute,
        routeType = DiscoverRoute::class,
    ),

    RELATE(
        selectedIcon = Icons.Rounded.Favorite,
        unselectedIcon = Icons.Rounded.FavoriteBorder,
        route = RelateRoute,
        routeType = RelateRoute::class,
    ),

    CHAT(
        selectedIcon = Icons.Rounded.Email,
        unselectedIcon = Icons.Rounded.MailOutline,
        route = ChatRoute,
        routeType = ChatRoute::class,
    ),

    PROFILE(
        selectedIcon = Icons.Rounded.AccountCircle,
        unselectedIcon = Icons.Rounded.Person,
        route = ProfileRoute,
        routeType = ProfileRoute::class,
    )
}
