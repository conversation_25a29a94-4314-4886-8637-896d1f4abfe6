package com.flutterup.network

import com.flutterup.base.utils.Timber
import com.squareup.moshi.Moshi
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.moshi.MoshiConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

class NetworkService(
    private val params: Params,
    private val moshi: Moshi
) {

    data class Params(
        val host: String,

        val connectTimeout: Long = DEFAULT_TIME_OUT,

        val readTimeout: Long = DEFAULT_TIME_OUT,

        val writeTimeout: Long = DEFAULT_TIME_OUT,

        val retryOnConnectionFailure: Boolean = false,

        val interceptors: List<Interceptor> = emptyList(),

        val level: HttpLoggingInterceptor.Level = HttpLoggingInterceptor.Level.BODY,
    )

    companion object {
        private const val PREFIX = "https://"

        private const val DEFAULT_TIME_OUT = 30_000L
    }

    private val retrofit: Retrofit

    private val serviceHolder: ConcurrentHashMap<Class<*>, Any>

    init {
        val baseUrl = PREFIX + params.host

        this.retrofit = Retrofit.Builder()
            .baseUrl(baseUrl)
            .addConverterFactory(MoshiConverterFactory.create(moshi))
            .addConverterFactory(ScalarsConverterFactory.create())
            .client(createOkHttpClient())
            .build()

        this.serviceHolder = ConcurrentHashMap()
    }

    @Suppress("UNCHECKED_CAST")
    operator fun <T : Any> get(clazz: Class<T>): T =
        serviceHolder.getOrPut(clazz) { retrofit.create<T>(clazz) } as T

    private fun createOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor(logger = Logger()).apply {
            setLevel(params.level)
        }

        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .addInterceptor(ResponseSuccessfulInterceptor())
            .apply {
                if (params.connectTimeout > 0) {
                    connectTimeout(params.connectTimeout, TimeUnit.MILLISECONDS)
                }
                if (params.readTimeout > 0) {
                    readTimeout(params.readTimeout, TimeUnit.MILLISECONDS)
                }
                if (params.writeTimeout > 0) {
                    writeTimeout(params.writeTimeout, TimeUnit.MILLISECONDS)
                }
                if (params.retryOnConnectionFailure) {
                    retryOnConnectionFailure(true)
                }
                if (params.interceptors.isNotEmpty()) {
                    for (interceptor in params.interceptors) {
                        addInterceptor(interceptor)
                    }
                }
            }
            .build()
    }

    private class Logger : HttpLoggingInterceptor.Logger {
        override fun log(message: String) {
            Timber.d(message)
        }
    }
}