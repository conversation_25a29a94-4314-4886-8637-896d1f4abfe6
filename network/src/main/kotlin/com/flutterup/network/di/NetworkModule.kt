package com.flutterup.network.di

import com.flutterup.network.NetworkService
import com.flutterup.network.utils.ConnectivityManagerNetworkMonitor
import com.flutterup.network.utils.NetworkMonitor
import com.squareup.moshi.Moshi
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class NetworkModule {

    @Binds
    internal abstract fun bindNetworkMonitor(networkMonitor: ConnectivityManagerNetworkMonitor): NetworkMonitor

    companion object {
        @Singleton
        @Provides
        fun provideNetworkService(params: NetworkService.Params, moshi: Moshi): NetworkService {
            return NetworkService(params, moshi)
        }
    }
}