package com.flutterup.network

import android.os.Parcelable
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@JsonClass(generateAdapter = true)
data class BaseResponse<T>(
    @<PERSON>son(name = "code")
    val code: Int,

    @<PERSON><PERSON>(name = "msg")
    val message: String? = null,

    @Json(name = "data")
    val data: T? = null,

    @<PERSON><PERSON>(name = "action")
    val action: Action? = null
) {
    fun isSuccess(): Boolean = code == 0
}


@JsonClass(generateAdapter = true)
@Parcelize
data class Action(
    @Json(name = "id")
    val id: Int? = null,

    @<PERSON><PERSON>(name = "param")
    val params: @RawValue Any? = null,

    @<PERSON><PERSON>(name = "trace_id")
    val traceId: Long? = null,
) : Parcelable